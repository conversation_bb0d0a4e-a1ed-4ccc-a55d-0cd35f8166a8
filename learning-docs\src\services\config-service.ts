import * as vscode from 'vscode';
import { IConfigService } from '../interfaces/config';

/**
 * Implementation of the configuration service.
 */
export class ConfigService implements IConfigService {
    private readonly configSection = 'learningDocs';
    
    /**
     * Get a configuration setting by key.
     * @param key The configuration key.
     * @returns The configuration value, or undefined if not found.
     */
    public getSetting<T>(key: string): T | undefined {
        const config = vscode.workspace.getConfiguration(this.configSection);
        return config.get<T>(key);
    }
    
    /**
     * Check if a feature is enabled.
     * @param featureKey The feature key to check.
     * @returns True if the feature is enabled, false otherwise.
     */
    public isFeatureEnabled(featureKey: string): boolean {
        return this.getSetting<boolean>(`enable.${featureKey}`) ?? false;
    }
}
