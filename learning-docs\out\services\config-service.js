"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigService = void 0;
const vscode = __importStar(require("vscode"));
/**
 * Implementation of the configuration service.
 */
class ConfigService {
    configSection = 'learningDocs';
    /**
     * Get a configuration setting by key.
     * @param key The configuration key.
     * @returns The configuration value, or undefined if not found.
     */
    getSetting(key) {
        const config = vscode.workspace.getConfiguration(this.configSection);
        return config.get(key);
    }
    /**
     * Check if a feature is enabled.
     * @param featureKey The feature key to check.
     * @returns True if the feature is enabled, false otherwise.
     */
    isFeatureEnabled(featureKey) {
        return this.getSetting(`enable.${featureKey}`) ?? false;
    }
}
exports.ConfigService = ConfigService;
//# sourceMappingURL=config-service.js.map