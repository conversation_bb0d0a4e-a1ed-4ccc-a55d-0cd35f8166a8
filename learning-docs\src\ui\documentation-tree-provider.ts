import * as vscode from 'vscode';
import * as path from 'path';
import { ILoggerService } from '../interfaces/logger';
import { IStorageService } from '../interfaces/storage-service';
import { ChangeDocumentationRecord } from '../interfaces/storage';

/**
 * Tree item for a documentation record.
 */
export class DocumentationTreeItem extends vscode.TreeItem {
    constructor(
        public readonly record: ChangeDocumentationRecord,
        public readonly collapsibleState: vscode.TreeItemCollapsibleState
    ) {
        super(DocumentationTreeItem.getLabel(record), collapsibleState);

        this.tooltip = `${record.filePath} (${record.timestamp})`;
        this.description = record.timestamp;
        this.id = record.id;

        // Set the icon based on the event type
        switch (record.eventType) {
            case 'create':
                this.iconPath = new vscode.ThemeIcon('add');
                break;
            case 'modify':
                this.iconPath = new vscode.ThemeIcon('edit');
                break;
            case 'delete':
                this.iconPath = new vscode.ThemeIcon('trash');
                break;
            case 'baseline':
                this.iconPath = new vscode.ThemeIcon('file');
                break;
        }

        // Set the command to open the documentation panel
        this.command = {
            command: 'learningDocs.showDocumentation',
            title: 'Show Documentation',
            arguments: [record.id]
        };
    }

    /**
     * Get the label for a documentation record.
     * @param record The documentation record.
     * @returns The label for the record.
     */
    private static getLabel(record: ChangeDocumentationRecord): string {
        const fileName = path.basename(record.filePath);

        switch (record.eventType) {
            case 'create':
                return `Created ${fileName}`;
            case 'modify':
                return `Modified ${fileName}`;
            case 'delete':
                return `Deleted ${fileName}`;
            case 'baseline':
                return `Baseline ${fileName}`;
            default:
                return fileName;
        }
    }
}

/**
 * Tree item for a file.
 */
export class FileTreeItem extends vscode.TreeItem {
    constructor(
        public readonly filePath: string,
        public readonly collapsibleState: vscode.TreeItemCollapsibleState
    ) {
        super(path.basename(filePath), collapsibleState);

        this.tooltip = filePath;
        this.description = filePath;
        this.contextValue = 'file';
        this.iconPath = vscode.ThemeIcon.File;
    }
}

/**
 * Tree data provider for documentation records.
 */
export class DocumentationTreeProvider implements vscode.TreeDataProvider<vscode.TreeItem> {
    private _onDidChangeTreeData: vscode.EventEmitter<vscode.TreeItem | undefined | null | void> = new vscode.EventEmitter<vscode.TreeItem | undefined | null | void>();
    readonly onDidChangeTreeData: vscode.Event<vscode.TreeItem | undefined | null | void> = this._onDidChangeTreeData.event;

    constructor(
        private readonly loggerService: ILoggerService,
        private readonly storageService: IStorageService
    ) {}

    /**
     * Refresh the tree view.
     */
    public refresh(): void {
        this._onDidChangeTreeData.fire();
    }

    /**
     * Get the tree item for a given element.
     * @param element The element to get the tree item for.
     * @returns The tree item for the element.
     */
    getTreeItem(element: vscode.TreeItem): vscode.TreeItem {
        return element;
    }

    /**
     * Get the children of a given element.
     * @param element The element to get the children for.
     * @returns A promise that resolves to the children of the element.
     */
    async getChildren(element?: vscode.TreeItem): Promise<vscode.TreeItem[]> {
        try {
            if (!element) {
                // Root level: show files with documentation
                return await this.getFileItems();
            } else if (element instanceof FileTreeItem) {
                // File level: show documentation records for the file
                return await this.getRecordItems(element.filePath);
            } else {
                // No children for documentation records
                return [];
            }
        } catch (error) {
            this.loggerService.error(`Error getting tree items: ${error}`);
            return [];
        }
    }

    /**
     * Get the file items for the root level.
     * @returns A promise that resolves to the file items.
     */
    private async getFileItems(): Promise<FileTreeItem[]> {
        try {
            // Get all unique file paths from the storage
            const filePaths = await this.storageService.getAllFilePaths();

            // Create tree items for each file
            return filePaths.map(filePath => new FileTreeItem(
                filePath,
                vscode.TreeItemCollapsibleState.Collapsed
            ));
        } catch (error) {
            this.loggerService.error(`Error getting file items: ${error}`);
            return [];
        }
    }

    /**
     * Get the record items for a file.
     * @param filePath The path to the file.
     * @returns A promise that resolves to the record items.
     */
    private async getRecordItems(filePath: string): Promise<DocumentationTreeItem[]> {
        try {
            // Get records for the file
            const records = await this.storageService.getRecordsByFile(filePath);

            // Create tree items for each record
            return records.map(record => new DocumentationTreeItem(
                record,
                vscode.TreeItemCollapsibleState.None
            ));
        } catch (error) {
            this.loggerService.error(`Error getting record items: ${error}`);
            return [];
        }
    }
}
