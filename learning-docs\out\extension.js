"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.activate = activate;
exports.deactivate = deactivate;
// The module 'vscode' contains the VS Code extensibility API
// Import the module and reference it with the alias vscode in your code below
const vscode = __importStar(require("vscode"));
// Import services
const logger_service_1 = require("./services/logger-service");
const config_service_1 = require("./services/config-service");
const workspace_service_1 = require("./services/workspace-service");
const storage_service_1 = require("./services/storage-service");
const file_history_service_1 = require("./services/file-history-service");
const diff_service_1 = require("./services/diff-service");
const semantic_analyzer_service_1 = require("./services/semantic-analyzer-service");
const context_analyzer_service_1 = require("./services/context-analyzer-service");
const ai_documentation_service_1 = require("./services/ai-documentation-service");
const change_event_aggregator_service_1 = require("./services/change-event-aggregator-service");
const change_processor_service_1 = require("./services/change-processor-service");
const workspace_initializer_service_1 = require("./services/workspace-initializer-service");
// Import UI components
const documentation_panel_1 = require("./ui/documentation-panel");
const documentation_tree_provider_1 = require("./ui/documentation-tree-provider");
const status_bar_item_1 = require("./ui/status-bar-item");
// This method is called when your extension is activated
// Your extension is activated the very first time the command is executed
async function activate(context) {
    // Initialize services
    const loggerService = new logger_service_1.LoggerService();
    const configService = new config_service_1.ConfigService();
    const workspaceService = new workspace_service_1.WorkspaceService(configService, loggerService);
    const storageService = new storage_service_1.StorageService(context, loggerService, workspaceService);
    const fileHistoryService = new file_history_service_1.FileHistoryService(context, loggerService, workspaceService);
    const diffService = new diff_service_1.DiffService(loggerService);
    const semanticAnalyzerService = new semantic_analyzer_service_1.SemanticAnalyzerService(loggerService, configService, workspaceService);
    const contextAnalyzerService = new context_analyzer_service_1.ContextAnalyzerService(loggerService, configService, workspaceService);
    const aiDocumentationService = new ai_documentation_service_1.AIDocumentationService(loggerService, configService);
    const changeEventAggregator = new change_event_aggregator_service_1.ChangeEventAggregatorService(loggerService, workspaceService);
    const changeProcessor = new change_processor_service_1.ChangeProcessorService(loggerService, changeEventAggregator, storageService, fileHistoryService, diffService, semanticAnalyzerService, contextAnalyzerService, aiDocumentationService, configService);
    // Initialize workspace initializer
    const workspaceInitializer = new workspace_initializer_service_1.WorkspaceInitializerService(context, loggerService, workspaceService, storageService, fileHistoryService, diffService);
    // Perform workspace initialization before starting change tracking
    try {
        await workspaceInitializer.initializeWorkspace();
    }
    catch (error) {
        loggerService.error(`Workspace initialization failed: ${error}`);
        // Continue with extension activation even if initialization fails
    }
    // Start the change processor after initialization
    changeProcessor.start();
    // Initialize UI components
    const treeDataProvider = new documentation_tree_provider_1.DocumentationTreeProvider(loggerService, storageService);
    const statusBarItem = new status_bar_item_1.StatusBarItem(loggerService);
    // Register tree view
    const treeView = vscode.window.createTreeView('learningDocsExplorer', {
        treeDataProvider
    });
    // Register commands
    context.subscriptions.push(vscode.commands.registerCommand('learningDocs.showDocumentationPanel', () => {
        documentation_panel_1.DocumentationPanel.createOrShow(context.extensionUri, loggerService, storageService);
    }), vscode.commands.registerCommand('learningDocs.showDocumentation', async (recordId) => {
        const panel = documentation_panel_1.DocumentationPanel.createOrShow(context.extensionUri, loggerService, storageService);
        if (recordId) {
            // Show documentation for a specific record
            await panel.showDocumentationForRecord(recordId);
        }
        else if (vscode.window.activeTextEditor) {
            // Show documentation for the active file
            const filePath = workspaceService.getRelativePath(vscode.window.activeTextEditor.document.uri);
            if (filePath) {
                await panel.showDocumentationForFile(filePath);
            }
        }
    }), vscode.commands.registerCommand('learningDocs.refreshDocumentation', () => {
        treeDataProvider.refresh();
    }));
    // Add services and UI components to disposables
    context.subscriptions.push(treeView, statusBarItem, { dispose: () => changeProcessor.dispose() }, { dispose: () => changeEventAggregator.dispose() }, { dispose: () => loggerService.dispose() });
    loggerService.info('Learning Docs extension activated');
}
// This method is called when your extension is deactivated
function deactivate() {
    console.log('Learning Docs extension deactivated');
}
//# sourceMappingURL=extension.js.map