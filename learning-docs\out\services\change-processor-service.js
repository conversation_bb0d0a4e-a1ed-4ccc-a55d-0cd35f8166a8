"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChangeProcessorService = void 0;
/**
 * Implementation of the change processor service.
 */
class ChangeProcessorService {
    loggerService;
    changeEventAggregator;
    storageService;
    fileHistoryService;
    diffService;
    semanticAnalyzerService;
    contextAnalyzerService;
    aiDocumentationService;
    configService;
    disposables = [];
    constructor(loggerService, changeEventAggregator, storageService, fileHistoryService, diffService, semanticAnalyzerService, contextAnalyzerService, aiDocumentationService, configService) {
        this.loggerService = loggerService;
        this.changeEventAggregator = changeEventAggregator;
        this.storageService = storageService;
        this.fileHistoryService = fileHistoryService;
        this.diffService = diffService;
        this.semanticAnalyzerService = semanticAnalyzerService;
        this.contextAnalyzerService = contextAnalyzerService;
        this.aiDocumentationService = aiDocumentationService;
        this.configService = configService;
    }
    /**
     * Start processing changes.
     */
    start() {
        this.loggerService.info('Starting change processor');
        // Start the change event aggregator
        this.changeEventAggregator.startListening();
        // Subscribe to stable change events
        this.disposables.push(this.changeEventAggregator.onDidStableChange(this.processChange.bind(this)));
    }
    /**
     * Stop processing changes.
     */
    stop() {
        this.loggerService.info('Stopping change processor');
        // Stop the change event aggregator
        this.changeEventAggregator.stopListening();
        // Dispose all disposables
        for (const disposable of this.disposables) {
            disposable.dispose();
        }
        this.disposables = [];
    }
    /**
     * Process a change event.
     * @param change The change to process.
     */
    async processChange(change) {
        this.loggerService.info(`Processing change: ${change.eventType} - ${change.filePath}`);
        try {
            // Create a documentation record from the change
            const record = {
                ...change
            };
            // For 'modify' events, get the previous content
            if (change.eventType === 'modify' && this.fileHistoryService) {
                record.previousContent = await this.fileHistoryService.getPreviousVersion(change.filePath);
            }
            // Generate diff if we have both previous and current content
            if (this.diffService &&
                (change.eventType === 'modify' || change.eventType === 'create' || change.eventType === 'baseline') &&
                change.currentContent) {
                const previousContent = record.previousContent || '';
                const currentContent = change.currentContent;
                // Generate unified diff
                record.rawDiff = await this.diffService.generateUnifiedDiff(previousContent, currentContent, change.filePath, change.filePath);
            }
            // Perform semantic analysis if enabled
            if (this.semanticAnalyzerService &&
                this.configService?.isFeatureEnabled('semanticAnalysis') &&
                (change.eventType === 'modify' || change.eventType === 'create' || change.eventType === 'baseline') &&
                change.currentContent) {
                try {
                    const semanticAnalysis = await this.semanticAnalyzerService.getSemanticAnalysis(change.filePath, record.previousContent, change.currentContent);
                    if (semanticAnalysis) {
                        record.semanticPrimitives = semanticAnalysis.primitives;
                        this.loggerService.info(`Semantic analysis completed for ${change.filePath}: ${semanticAnalysis.primitives.length} primitives found`);
                    }
                }
                catch (error) {
                    this.loggerService.error(`Semantic analysis failed: ${error}`);
                }
            }
            // Perform contextual analysis if enabled
            if (this.contextAnalyzerService &&
                this.configService?.isFeatureEnabled('contextualAnalysis') &&
                (change.eventType === 'modify' || change.eventType === 'create' || change.eventType === 'baseline')) {
                try {
                    const contextualInfo = await this.contextAnalyzerService.getContextualInfo(change.filePath, record.semanticPrimitives);
                    if (contextualInfo) {
                        record.contextualInfo = contextualInfo;
                        this.loggerService.info(`Contextual analysis completed for ${change.filePath}`);
                    }
                }
                catch (error) {
                    this.loggerService.error(`Contextual analysis failed: ${error}`);
                }
            }
            // Generate AI documentation if enabled
            if (this.aiDocumentationService &&
                this.configService?.isFeatureEnabled('aiDocumentation') &&
                (change.eventType === 'modify' || change.eventType === 'create' || change.eventType === 'baseline')) {
                try {
                    // We need to save the record first to ensure it has all the data from previous steps
                    const tempRecord = { ...record };
                    const aiAnalysis = await this.aiDocumentationService.generateDocumentation(tempRecord);
                    if (aiAnalysis) {
                        record.aiAnalysis = aiAnalysis;
                        this.loggerService.info(`AI documentation generated for ${change.filePath}`);
                    }
                }
                catch (error) {
                    this.loggerService.error(`AI documentation generation failed: ${error}`);
                }
            }
            // Save the record
            await this.storageService.saveDocumentationRecord(record);
            // Update the file history cache with the current content
            if (this.fileHistoryService &&
                (change.eventType === 'modify' || change.eventType === 'create' || change.eventType === 'baseline') &&
                change.currentContent) {
                await this.fileHistoryService.updateCache(change.filePath, change.currentContent);
            }
            this.loggerService.info(`Successfully processed change: ${change.id}`);
        }
        catch (error) {
            this.loggerService.error(`Failed to process change: ${error}`);
        }
    }
    /**
     * Dispose of resources.
     */
    dispose() {
        this.stop();
    }
}
exports.ChangeProcessorService = ChangeProcessorService;
//# sourceMappingURL=change-processor-service.js.map