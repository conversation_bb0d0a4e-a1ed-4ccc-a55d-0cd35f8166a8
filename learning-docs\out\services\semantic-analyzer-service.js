"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.SemanticAnalyzerService = void 0;
const vscode = __importStar(require("vscode"));
/**
 * Implementation of the semantic analyzer service.
 */
class SemanticAnalyzerService {
    loggerService;
    configService;
    workspaceService;
    constructor(loggerService, configService, workspaceService) {
        this.loggerService = loggerService;
        this.configService = configService;
        this.workspaceService = workspaceService;
    }
    /**
     * Get semantic analysis for a change.
     *
     * @param previousContent The previous content of the file (can be undefined for new files).
     * @param currentContent The current content of the file.
     * @param uri The URI of the file for context and LSP calls.
     * @param languageId The language ID of the file.
     * @returns A promise that resolves to the semantic analysis result, or null if analysis is not possible.
     */
    async getSemanticAnalysis(previousContent, currentContent, uri, languageId) {
        try {
            // Check if semantic analysis is enabled
            if (!this.configService.isFeatureEnabled('semanticAnalysis')) {
                this.loggerService.info('Semantic analysis is disabled');
                return null;
            }
            // Check if language is supported
            if (!this.isLanguageSupported(languageId)) {
                this.loggerService.info(`Semantic analysis not enabled for language: ${languageId}`);
                return null;
            }
            // Check text similarity threshold
            if (previousContent && this.shouldSkipDueToSimilarity(previousContent, currentContent)) {
                this.loggerService.info('Skipping semantic analysis due to high text similarity');
                return null;
            }
            this.loggerService.info(`Starting semantic analysis for ${languageId} file: ${uri.fsPath}`);
            // Get document symbols for current content
            const currentSymbols = await this.getDocumentSymbols(currentContent, languageId, uri);
            if (!currentSymbols) {
                this.loggerService.warn('Failed to get current document symbols');
                return null;
            }
            // Get document symbols for previous content (if exists)
            let previousSymbols;
            if (previousContent) {
                previousSymbols = await this.getDocumentSymbols(previousContent, languageId);
            }
            // Diff the symbol trees
            const primitives = this.diffSymbolTrees(previousSymbols || [], currentSymbols);
            // Calculate confidence and summary
            const confidence = this.calculateConfidence(primitives, currentSymbols);
            const summary = this.calculateSummary(primitives);
            this.loggerService.info(`Semantic analysis completed: ${primitives.length} primitives found`);
            return {
                primitives,
                languageId,
                confidence,
                summary
            };
        }
        catch (error) {
            this.loggerService.error(`Error during semantic analysis: ${error}`);
            return null;
        }
    }
    /**
     * Check if semantic analysis is enabled for a language.
     * @param languageId The language ID.
     * @returns True if semantic analysis is enabled, false otherwise.
     */
    isLanguageSupported(languageId) {
        const enabledLanguages = this.configService.getSetting('semanticAnalysis.enabledLanguages') || [
            'typescript', 'javascript', 'typescriptreact', 'javascriptreact', 'python', 'java', 'csharp', 'go', 'ruby', 'php'
        ];
        return enabledLanguages.includes(languageId);
    }
    /**
     * Check if semantic analysis should be skipped due to high text similarity.
     * @param previousContent The previous content.
     * @param currentContent The current content.
     * @returns True if analysis should be skipped, false otherwise.
     */
    shouldSkipDueToSimilarity(previousContent, currentContent) {
        const threshold = this.configService.getSetting('semanticAnalysis.skipIfTextSimilarityAbove') || 0.98;
        // Simple similarity calculation based on character differences
        const maxLength = Math.max(previousContent.length, currentContent.length);
        if (maxLength === 0) {
            return true;
        }
        const similarity = 1 - (Math.abs(previousContent.length - currentContent.length) / maxLength);
        return similarity > threshold;
    }
    /**
     * Get the language ID from a file extension.
     * @param extension The file extension.
     * @returns The language ID, or undefined if not found.
     */
    getLanguageIdFromExtension(extension) {
        const extensionToLanguageMap = {
            '.ts': 'typescript',
            '.js': 'javascript',
            '.tsx': 'typescriptreact',
            '.jsx': 'javascriptreact',
            '.py': 'python',
            '.java': 'java',
            '.cs': 'csharp',
            '.go': 'go',
            '.rb': 'ruby',
            '.php': 'php',
            '.html': 'html',
            '.css': 'css',
            '.json': 'json',
            '.md': 'markdown'
        };
        return extensionToLanguageMap[extension];
    }
    /**
     * Get document symbols for file content.
     * @param content The file content.
     * @param languageId The language ID.
     * @param tempUri Optional URI for context (used for current content).
     * @returns A promise that resolves to an array of document symbols, or undefined if failed.
     */
    async getDocumentSymbols(content, languageId, tempUri) {
        try {
            // For current content, use the provided URI if available
            if (tempUri) {
                // Try to get symbols from the current document if it's already open
                const openDoc = vscode.workspace.textDocuments.find(doc => doc.uri.toString() === tempUri.toString());
                if (openDoc && openDoc.getText() === content) {
                    const symbols = await vscode.commands.executeCommand('vscode.executeDocumentSymbolProvider', openDoc.uri);
                    return symbols || [];
                }
            }
            // Create a temporary document with the content
            const document = await vscode.workspace.openTextDocument({
                language: languageId,
                content: content
            });
            // Get document symbols using the language server
            const symbols = await vscode.commands.executeCommand('vscode.executeDocumentSymbolProvider', document.uri);
            // Note: We don't explicitly close the temporary document here as VSCode manages it
            // and closing it might interfere with the LSP analysis
            return symbols || [];
        }
        catch (error) {
            this.loggerService.error(`Error getting document symbols: ${error}`);
            return undefined;
        }
    }
    /**
     * Diff two symbol trees to find semantic changes.
     * @param previousSymbols The previous symbols.
     * @param currentSymbols The current symbols.
     * @returns An array of semantic primitives representing the changes.
     */
    diffSymbolTrees(previousSymbols, currentSymbols) {
        const primitives = [];
        const maxDepth = this.configService.getSetting('semanticAnalysis.maxNestingDepth') || 3;
        // Create maps for quick lookup
        const previousSymbolMap = new Map();
        const currentSymbolMap = new Map();
        // Flatten symbol trees and create maps
        this.flattenSymbolTree(previousSymbols, previousSymbolMap, '', 0, maxDepth);
        this.flattenSymbolTree(currentSymbols, currentSymbolMap, '', 0, maxDepth);
        // Find added symbols
        for (const [key, symbol] of currentSymbolMap.entries()) {
            if (!previousSymbolMap.has(key)) {
                primitives.push(this.createPrimitiveFromSymbol(symbol, 'add', key));
            }
        }
        // Find removed symbols
        for (const [key, symbol] of previousSymbolMap.entries()) {
            if (!currentSymbolMap.has(key)) {
                primitives.push(this.createPrimitiveFromSymbol(symbol, 'remove', key));
            }
        }
        // Find modified symbols
        for (const [key, currentSymbol] of currentSymbolMap.entries()) {
            const previousSymbol = previousSymbolMap.get(key);
            if (previousSymbol && this.hasSymbolChanged(previousSymbol, currentSymbol)) {
                primitives.push(this.createModifiedPrimitive(previousSymbol, currentSymbol, key));
            }
        }
        // Attempt rename detection if enabled
        if (this.configService.getSetting('semanticAnalysis.enableRenameDetection')) {
            this.detectRenames(primitives);
        }
        return primitives;
    }
    /**
     * Flatten a symbol tree into a map for easier comparison.
     * @param symbols The symbols to flatten.
     * @param map The map to populate.
     * @param containerPath The path of the container.
     * @param currentDepth The current nesting depth.
     * @param maxDepth The maximum nesting depth to process.
     */
    flattenSymbolTree(symbols, map, containerPath, currentDepth, maxDepth) {
        if (currentDepth >= maxDepth) {
            return;
        }
        for (const symbol of symbols) {
            const path = containerPath ? `${containerPath}.${symbol.name}` : symbol.name;
            const key = `${path}:${symbol.kind}`;
            map.set(key, symbol);
            if (symbol.children && symbol.children.length > 0) {
                this.flattenSymbolTree(symbol.children, map, path, currentDepth + 1, maxDepth);
            }
        }
    }
    /**
     * Check if a symbol has changed.
     * @param previousSymbol The previous symbol.
     * @param currentSymbol The current symbol.
     * @returns True if the symbol has changed, false otherwise.
     */
    hasSymbolChanged(previousSymbol, currentSymbol) {
        // Check if the detail (signature) has changed
        if (previousSymbol.detail !== currentSymbol.detail) {
            return true;
        }
        // Check if the kind has changed
        if (previousSymbol.kind !== currentSymbol.kind) {
            return true;
        }
        // Check if the range has changed significantly
        const previousRange = previousSymbol.range;
        const currentRange = currentSymbol.range;
        const lineDiff = Math.abs((previousRange.end.line - previousRange.start.line) -
            (currentRange.end.line - currentRange.start.line));
        if (lineDiff > 1) {
            return true;
        }
        return false;
    }
    /**
     * Create a semantic primitive from a document symbol.
     * @param symbol The document symbol.
     * @param operation The type of operation.
     * @param symbolPath The full path of the symbol.
     * @returns A semantic primitive.
     */
    createPrimitiveFromSymbol(symbol, operation, symbolPath) {
        const parentPath = symbolPath.includes('.') ? symbolPath.substring(0, symbolPath.lastIndexOf('.')) : undefined;
        return {
            operation,
            elementType: this.getElementTypeFromSymbolKind(symbol.kind),
            elementName: symbol.name,
            signature: symbol.detail,
            range: {
                startLine: symbol.range.start.line,
                startCharacter: symbol.range.start.character,
                endLine: symbol.range.end.line,
                endCharacter: symbol.range.end.character
            },
            parentElement: parentPath ? {
                name: parentPath.split('.').pop() || '',
                type: 'unknown' // We'd need to track parent types for this
            } : undefined
        };
    }
    /**
     * Create a modified semantic primitive from two document symbols.
     * @param previousSymbol The previous symbol.
     * @param currentSymbol The current symbol.
     * @param symbolPath The full path of the symbol.
     * @returns A semantic primitive.
     */
    createModifiedPrimitive(previousSymbol, currentSymbol, symbolPath) {
        const parentPath = symbolPath.includes('.') ? symbolPath.substring(0, symbolPath.lastIndexOf('.')) : undefined;
        return {
            operation: 'modify',
            elementType: this.getElementTypeFromSymbolKind(currentSymbol.kind),
            elementName: currentSymbol.name,
            signature: currentSymbol.detail,
            range: {
                startLine: currentSymbol.range.start.line,
                startCharacter: currentSymbol.range.start.character,
                endLine: currentSymbol.range.end.line,
                endCharacter: currentSymbol.range.end.character
            },
            parentElement: parentPath ? {
                name: parentPath.split('.').pop() || '',
                type: 'unknown'
            } : undefined
        };
    }
    /**
     * Get the semantic primitive element type from a symbol kind.
     * @param kind The symbol kind.
     * @returns The semantic primitive element type.
     */
    getElementTypeFromSymbolKind(kind) {
        switch (kind) {
            case vscode.SymbolKind.Function:
            case vscode.SymbolKind.Constructor:
                return 'function';
            case vscode.SymbolKind.Class:
                return 'class';
            case vscode.SymbolKind.Method:
                return 'method';
            case vscode.SymbolKind.Property:
            case vscode.SymbolKind.Field:
                return 'property';
            case vscode.SymbolKind.Interface:
                return 'interface';
            case vscode.SymbolKind.Enum:
                return 'enum';
            case vscode.SymbolKind.Variable:
            case vscode.SymbolKind.Constant:
                return 'variable';
            default:
                return 'unknown';
        }
    }
    /**
     * Calculate confidence score for the analysis.
     * @param primitives The extracted primitives.
     * @param currentSymbols The current symbols.
     * @returns A confidence score between 0 and 1.
     */
    calculateConfidence(primitives, currentSymbols) {
        // Simple confidence calculation based on symbol extraction success
        if (currentSymbols.length === 0) {
            return primitives.length === 0 ? 1.0 : 0.5;
        }
        // Higher confidence if we found symbols and changes
        return primitives.length > 0 ? 0.9 : 0.8;
    }
    /**
     * Calculate summary of changes.
     * @param primitives The extracted primitives.
     * @returns A summary of the changes.
     */
    calculateSummary(primitives) {
        const summary = {
            added: 0,
            removed: 0,
            modified: 0,
            renamed: 0
        };
        for (const primitive of primitives) {
            switch (primitive.operation) {
                case 'add':
                    summary.added++;
                    break;
                case 'remove':
                    summary.removed++;
                    break;
                case 'modify':
                    summary.modified++;
                    break;
                case 'rename':
                    summary.renamed++;
                    break;
            }
        }
        return summary;
    }
    /**
     * Detect potential renames in the primitives list.
     * This is a simple heuristic-based approach.
     * @param primitives The primitives to analyze for renames.
     */
    detectRenames(primitives) {
        // Simple rename detection: look for add/remove pairs of same type with similar signatures
        const addedPrimitives = primitives.filter(p => p.operation === 'add');
        const removedPrimitives = primitives.filter(p => p.operation === 'remove');
        for (const added of addedPrimitives) {
            for (const removed of removedPrimitives) {
                if (added.elementType === removed.elementType &&
                    added.signature === removed.signature &&
                    added.elementName !== removed.elementName) {
                    // Convert to rename operation
                    added.operation = 'rename';
                    added.oldElementName = removed.elementName;
                    // Remove the removed primitive as it's now part of the rename
                    const removedIndex = primitives.indexOf(removed);
                    if (removedIndex > -1) {
                        primitives.splice(removedIndex, 1);
                    }
                    break;
                }
            }
        }
    }
}
exports.SemanticAnalyzerService = SemanticAnalyzerService;
//# sourceMappingURL=semantic-analyzer-service.js.map