import * as vscode from 'vscode';
import * as path from 'path';
import { ISemanticAnalyzerService, SemanticAnalysisResult, SemanticPrimitive } from '../interfaces/semantic-analyzer';
import { ILoggerService } from '../interfaces/logger';
import { IConfigService } from '../interfaces/config';
import { IWorkspaceService } from '../interfaces/workspace';

/**
 * Implementation of the semantic analyzer service.
 */
export class SemanticAnalyzerService implements ISemanticAnalyzerService {
    constructor(
        private readonly loggerService: ILoggerService,
        private readonly configService: IConfigService,
        private readonly workspaceService: IWorkspaceService
    ) {}
    
    /**
     * Get semantic analysis for a change.
     * 
     * @param filePath The path to the file.
     * @param previousContent The previous content of the file.
     * @param currentContent The current content of the file.
     * @returns A promise that resolves to the semantic analysis result, or null if analysis is not possible.
     */
    public async getSemanticAnalysis(
        filePath: string,
        previousContent: string | undefined,
        currentContent: string | undefined
    ): Promise<SemanticAnalysisResult | null> {
        try {
            // Check if semantic analysis is enabled for this file type
            const fileExtension = path.extname(filePath);
            if (!this.isSemanticAnalysisEnabledForExtension(fileExtension)) {
                this.loggerService.info(`Semantic analysis not enabled for extension: ${fileExtension}`);
                return null;
            }
            
            // Get the language ID for the file
            const languageId = this.getLanguageIdFromExtension(fileExtension);
            if (!languageId) {
                this.loggerService.warn(`Could not determine language ID for file: ${filePath}`);
                return null;
            }
            
            // If we don't have both previous and current content, we can't do a proper diff
            if (!previousContent && !currentContent) {
                this.loggerService.warn(`No content available for semantic analysis of: ${filePath}`);
                return null;
            }
            
            // Get document symbols for previous and current content
            const previousSymbols = previousContent 
                ? await this.getDocumentSymbols(previousContent, filePath, languageId) 
                : [];
                
            const currentSymbols = currentContent 
                ? await this.getDocumentSymbols(currentContent, filePath, languageId) 
                : [];
            
            // Diff the symbol trees to find semantic changes
            const primitives = this.diffSymbolTrees(previousSymbols, currentSymbols);
            
            return {
                primitives,
                languageId
            };
        } catch (error) {
            this.loggerService.error(`Error during semantic analysis: ${error}`);
            return null;
        }
    }
    
    /**
     * Check if semantic analysis is enabled for a file extension.
     * @param extension The file extension.
     * @returns True if semantic analysis is enabled, false otherwise.
     */
    private isSemanticAnalysisEnabledForExtension(extension: string): boolean {
        const enabledExtensions = this.configService.getSetting<string[]>('semanticAnalysis.enabledExtensions') || [
            '.ts', '.js', '.tsx', '.jsx', '.py', '.java', '.cs', '.go', '.rb', '.php'
        ];
        
        return enabledExtensions.includes(extension);
    }
    
    /**
     * Get the language ID from a file extension.
     * @param extension The file extension.
     * @returns The language ID, or undefined if not found.
     */
    private getLanguageIdFromExtension(extension: string): string | undefined {
        const extensionToLanguageMap: Record<string, string> = {
            '.ts': 'typescript',
            '.js': 'javascript',
            '.tsx': 'typescriptreact',
            '.jsx': 'javascriptreact',
            '.py': 'python',
            '.java': 'java',
            '.cs': 'csharp',
            '.go': 'go',
            '.rb': 'ruby',
            '.php': 'php',
            '.html': 'html',
            '.css': 'css',
            '.json': 'json',
            '.md': 'markdown'
        };
        
        return extensionToLanguageMap[extension];
    }
    
    /**
     * Get document symbols for a file content.
     * @param content The file content.
     * @param filePath The file path.
     * @param languageId The language ID.
     * @returns A promise that resolves to an array of document symbols.
     */
    private async getDocumentSymbols(
        content: string,
        filePath: string,
        languageId: string
    ): Promise<vscode.DocumentSymbol[]> {
        try {
            // Create a temporary URI for the content
            const workspaceRoot = this.workspaceService.getWorkspaceRoot();
            if (!workspaceRoot) {
                return [];
            }
            
            // Create a temporary file URI
            const tempUri = workspaceRoot.with({ path: workspaceRoot.path + '/' + path.basename(filePath) });
            
            // Create a temporary document with the content
            const document = await vscode.workspace.openTextDocument({
                language: languageId,
                content: content
            });
            
            // Get document symbols using the language server
            const symbols = await vscode.commands.executeCommand<vscode.DocumentSymbol[]>(
                'vscode.executeDocumentSymbolProvider',
                document.uri
            ) || [];
            
            return symbols;
        } catch (error) {
            this.loggerService.error(`Error getting document symbols: ${error}`);
            return [];
        }
    }
    
    /**
     * Diff two symbol trees to find semantic changes.
     * @param previousSymbols The previous symbols.
     * @param currentSymbols The current symbols.
     * @returns An array of semantic primitives representing the changes.
     */
    private diffSymbolTrees(
        previousSymbols: vscode.DocumentSymbol[],
        currentSymbols: vscode.DocumentSymbol[]
    ): SemanticPrimitive[] {
        const primitives: SemanticPrimitive[] = [];
        
        // Create maps for quick lookup
        const previousSymbolMap = new Map<string, vscode.DocumentSymbol>();
        const currentSymbolMap = new Map<string, vscode.DocumentSymbol>();
        
        // Flatten symbol trees and create maps
        this.flattenSymbolTree(previousSymbols, previousSymbolMap, '');
        this.flattenSymbolTree(currentSymbols, currentSymbolMap, '');
        
        // Find added symbols
        for (const [key, symbol] of currentSymbolMap.entries()) {
            if (!previousSymbolMap.has(key)) {
                primitives.push(this.createPrimitiveFromSymbol(symbol, 'added'));
            }
        }
        
        // Find removed symbols
        for (const [key, symbol] of previousSymbolMap.entries()) {
            if (!currentSymbolMap.has(key)) {
                primitives.push(this.createPrimitiveFromSymbol(symbol, 'removed'));
            }
        }
        
        // Find modified symbols
        for (const [key, currentSymbol] of currentSymbolMap.entries()) {
            const previousSymbol = previousSymbolMap.get(key);
            if (previousSymbol && this.hasSymbolChanged(previousSymbol, currentSymbol)) {
                primitives.push(this.createModifiedPrimitive(previousSymbol, currentSymbol));
            }
        }
        
        return primitives;
    }
    
    /**
     * Flatten a symbol tree into a map for easier comparison.
     * @param symbols The symbols to flatten.
     * @param map The map to populate.
     * @param containerPath The path of the container.
     */
    private flattenSymbolTree(
        symbols: vscode.DocumentSymbol[],
        map: Map<string, vscode.DocumentSymbol>,
        containerPath: string
    ): void {
        for (const symbol of symbols) {
            const path = containerPath ? `${containerPath}.${symbol.name}` : symbol.name;
            map.set(path, symbol);
            
            if (symbol.children && symbol.children.length > 0) {
                this.flattenSymbolTree(symbol.children, map, path);
            }
        }
    }
    
    /**
     * Check if a symbol has changed.
     * @param previousSymbol The previous symbol.
     * @param currentSymbol The current symbol.
     * @returns True if the symbol has changed, false otherwise.
     */
    private hasSymbolChanged(
        previousSymbol: vscode.DocumentSymbol,
        currentSymbol: vscode.DocumentSymbol
    ): boolean {
        // Check if the detail (signature) has changed
        if (previousSymbol.detail !== currentSymbol.detail) {
            return true;
        }
        
        // Check if the kind has changed
        if (previousSymbol.kind !== currentSymbol.kind) {
            return true;
        }
        
        // Check if the range has changed significantly
        const previousRange = previousSymbol.range;
        const currentRange = currentSymbol.range;
        const lineDiff = Math.abs(
            (previousRange.end.line - previousRange.start.line) -
            (currentRange.end.line - currentRange.start.line)
        );
        
        if (lineDiff > 1) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Create a semantic primitive from a document symbol.
     * @param symbol The document symbol.
     * @param changeType The type of change.
     * @returns A semantic primitive.
     */
    private createPrimitiveFromSymbol(
        symbol: vscode.DocumentSymbol,
        changeType: 'added' | 'removed' | 'modified' | 'renamed'
    ): SemanticPrimitive {
        return {
            type: this.getTypeFromSymbolKind(symbol.kind),
            name: symbol.name,
            changeType,
            detail: symbol.detail,
            range: {
                startLine: symbol.range.start.line,
                startCharacter: symbol.range.start.character,
                endLine: symbol.range.end.line,
                endCharacter: symbol.range.end.character
            },
            children: symbol.children && symbol.children.length > 0
                ? symbol.children.map(child => this.createPrimitiveFromSymbol(child, changeType))
                : undefined
        };
    }
    
    /**
     * Create a modified semantic primitive from two document symbols.
     * @param previousSymbol The previous symbol.
     * @param currentSymbol The current symbol.
     * @returns A semantic primitive.
     */
    private createModifiedPrimitive(
        previousSymbol: vscode.DocumentSymbol,
        currentSymbol: vscode.DocumentSymbol
    ): SemanticPrimitive {
        return {
            type: this.getTypeFromSymbolKind(currentSymbol.kind),
            name: currentSymbol.name,
            changeType: 'modified',
            detail: currentSymbol.detail,
            originalDetail: previousSymbol.detail,
            range: {
                startLine: currentSymbol.range.start.line,
                startCharacter: currentSymbol.range.start.character,
                endLine: currentSymbol.range.end.line,
                endCharacter: currentSymbol.range.end.character
            }
        };
    }
    
    /**
     * Get the semantic primitive type from a symbol kind.
     * @param kind The symbol kind.
     * @returns The semantic primitive type.
     */
    private getTypeFromSymbolKind(kind: vscode.SymbolKind): SemanticPrimitive['type'] {
        switch (kind) {
            case vscode.SymbolKind.Function:
            case vscode.SymbolKind.Constructor:
                return 'function';
            case vscode.SymbolKind.Class:
                return 'class';
            case vscode.SymbolKind.Method:
                return 'method';
            case vscode.SymbolKind.Property:
            case vscode.SymbolKind.Field:
                return 'property';
            case vscode.SymbolKind.Interface:
                return 'interface';
            case vscode.SymbolKind.Enum:
                return 'enum';
            case vscode.SymbolKind.Variable:
            case vscode.SymbolKind.Constant:
                return 'variable';
            case vscode.SymbolKind.Module:
            case vscode.SymbolKind.Package:
                return 'import';
            default:
                return 'other';
        }
    }
}
