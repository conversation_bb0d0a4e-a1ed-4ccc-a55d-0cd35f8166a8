{"version": 3, "file": "documentation-tree-provider.js", "sourceRoot": "", "sources": ["../../src/ui/documentation-tree-provider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,2CAA6B;AAM7B;;GAEG;AACH,MAAa,yBAA0B,SAAQ,MAAM,CAAC,QAAQ;IAEtC;IACA;IAFpB,YACoB,SAA4B,EAC5B,QAAgB;QAEhC,KAAK,CAAC,yBAAyB,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,MAAM,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;QAH3E,cAAS,GAAT,SAAS,CAAmB;QAC5B,aAAQ,GAAR,QAAQ,CAAQ;QAIhC,IAAI,CAAC,OAAO,GAAG,yBAAyB,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;QAC/D,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC,SAAS,IAAI,EAAE,CAAC;QAC7C,IAAI,CAAC,EAAE,GAAG,GAAG,QAAQ,IAAI,SAAS,CAAC,WAAW,IAAI,SAAS,CAAC,SAAS,EAAE,CAAC;QACxE,IAAI,CAAC,YAAY,GAAG,mBAAmB,CAAC;QAExC,2CAA2C;QAC3C,QAAQ,SAAS,CAAC,SAAS,EAAE,CAAC;YAC1B,KAAK,KAAK;gBACN,IAAI,CAAC,QAAQ,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,MAAM,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,CAAC;gBACnF,MAAM;YACV,KAAK,QAAQ;gBACT,IAAI,CAAC,QAAQ,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC;gBACpF,MAAM;YACV,KAAK,QAAQ;gBACT,IAAI,CAAC,QAAQ,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,MAAM,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC,CAAC;gBACrF,MAAM;YACV,KAAK,QAAQ;gBACT,IAAI,CAAC,QAAQ,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,eAAe,EAAE,IAAI,MAAM,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,CAAC;gBAC5F,MAAM;QACd,CAAC;QAED,gEAAgE;QAChE,IAAI,CAAC,OAAO,GAAG;YACX,OAAO,EAAE,gCAAgC;YACzC,KAAK,EAAE,oBAAoB;YAC3B,SAAS,EAAE,CAAC,QAAQ,CAAC;SACxB,CAAC;IACN,CAAC;IAED;;;;OAIG;IACK,MAAM,CAAC,QAAQ,CAAC,SAA4B;QAChD,MAAM,eAAe,GAAG;YACpB,KAAK,EAAE,GAAG;YACV,QAAQ,EAAE,GAAG;YACb,QAAQ,EAAE,IAAI;YACd,QAAQ,EAAE,IAAI;SACjB,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QAEvB,MAAM,sBAAsB,GAAG,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAE9G,IAAI,SAAS,CAAC,SAAS,KAAK,QAAQ,IAAI,SAAS,CAAC,cAAc,EAAE,CAAC;YAC/D,OAAO,GAAG,eAAe,IAAI,sBAAsB,MAAM,SAAS,CAAC,cAAc,QAAQ,SAAS,CAAC,WAAW,GAAG,CAAC;QACtH,CAAC;QAED,OAAO,GAAG,eAAe,IAAI,sBAAsB,MAAM,SAAS,CAAC,WAAW,GAAG,CAAC;IACtF,CAAC;IAED;;;;OAIG;IACK,MAAM,CAAC,UAAU,CAAC,SAA4B;QAClD,IAAI,OAAO,GAAG,GAAG,SAAS,CAAC,SAAS,CAAC,WAAW,EAAE,IAAI,SAAS,CAAC,WAAW,KAAK,SAAS,CAAC,WAAW,EAAE,CAAC;QAExG,IAAI,SAAS,CAAC,SAAS,EAAE,CAAC;YACtB,OAAO,IAAI,gBAAgB,SAAS,CAAC,SAAS,EAAE,CAAC;QACrD,CAAC;QAED,IAAI,SAAS,CAAC,aAAa,EAAE,CAAC;YAC1B,OAAO,IAAI,aAAa,SAAS,CAAC,aAAa,CAAC,IAAI,IAAI,SAAS,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;QAC3F,CAAC;QAED,IAAI,SAAS,CAAC,KAAK,EAAE,CAAC;YAClB,OAAO,IAAI,qBAAqB,SAAS,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,IAAI,SAAS,CAAC,KAAK,CAAC,OAAO,GAAG,CAAC,EAAE,CAAC;QACnG,CAAC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;CACJ;AAhFD,8DAgFC;AAED;;GAEG;AACH,MAAa,qBAAsB,SAAQ,MAAM,CAAC,QAAQ;IAElC;IACA;IAFpB,YACoB,MAAiC,EACjC,gBAAiD;QAEjE,KAAK,CAAC,qBAAqB,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,gBAAgB,CAAC,CAAC;QAHhD,WAAM,GAAN,MAAM,CAA2B;QACjC,qBAAgB,GAAhB,gBAAgB,CAAiC;QAIjE,IAAI,CAAC,OAAO,GAAG,qBAAqB,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QACxD,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC,SAAS,CAAC;QACpC,IAAI,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;QACpB,IAAI,CAAC,YAAY,GAAG,qBAAqB,CAAC;QAE1C,uCAAuC;QACvC,QAAQ,MAAM,CAAC,SAAS,EAAE,CAAC;YACvB,KAAK,QAAQ;gBACT,IAAI,CAAC,QAAQ,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBAC5C,MAAM;YACV,KAAK,QAAQ;gBACT,IAAI,CAAC,QAAQ,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;gBAC7C,MAAM;YACV,KAAK,QAAQ;gBACT,IAAI,CAAC,QAAQ,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;gBAC9C,MAAM;YACV,KAAK,UAAU;gBACX,IAAI,CAAC,QAAQ,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;gBAC7C,MAAM;QACd,CAAC;QAED,kDAAkD;QAClD,IAAI,CAAC,OAAO,GAAG;YACX,OAAO,EAAE,gCAAgC;YACzC,KAAK,EAAE,oBAAoB;YAC3B,SAAS,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC;SACzB,CAAC;IACN,CAAC;IAED;;;;OAIG;IACK,MAAM,CAAC,QAAQ,CAAC,MAAiC;QACrD,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAChD,IAAI,SAAiB,CAAC;QAEtB,QAAQ,MAAM,CAAC,SAAS,EAAE,CAAC;YACvB,KAAK,QAAQ;gBACT,SAAS,GAAG,WAAW,QAAQ,EAAE,CAAC;gBAClC,MAAM;YACV,KAAK,QAAQ;gBACT,SAAS,GAAG,YAAY,QAAQ,EAAE,CAAC;gBACnC,MAAM;YACV,KAAK,QAAQ;gBACT,SAAS,GAAG,WAAW,QAAQ,EAAE,CAAC;gBAClC,MAAM;YACV,KAAK,UAAU;gBACX,SAAS,GAAG,YAAY,QAAQ,EAAE,CAAC;gBACnC,MAAM;YACV;gBACI,SAAS,GAAG,QAAQ,CAAC;QAC7B,CAAC;QAED,6CAA6C;QAC7C,IAAI,MAAM,CAAC,kBAAkB,IAAI,MAAM,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACpE,MAAM,KAAK,GAAG,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC;YAC/C,SAAS,IAAI,KAAK,KAAK,mBAAmB,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC;QACtE,CAAC;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;;;OAIG;IACK,MAAM,CAAC,UAAU,CAAC,MAAiC;QACvD,IAAI,OAAO,GAAG,GAAG,MAAM,CAAC,QAAQ,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;QAEzD,IAAI,MAAM,CAAC,kBAAkB,IAAI,MAAM,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACpE,OAAO,IAAI,uBAAuB,CAAC;YACnC,MAAM,CAAC,kBAAkB,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;gBAC1C,MAAM,eAAe,GAAG;oBACpB,KAAK,EAAE,GAAG;oBACV,QAAQ,EAAE,GAAG;oBACb,QAAQ,EAAE,IAAI;oBACd,QAAQ,EAAE,IAAI;iBACjB,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;gBAEvB,OAAO,IAAI,KAAK,eAAe,IAAI,SAAS,CAAC,WAAW,KAAK,SAAS,CAAC,WAAW,EAAE,CAAC;YACzF,CAAC,CAAC,CAAC;QACP,CAAC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;CACJ;AA/FD,sDA+FC;AAED;;GAEG;AACH,MAAa,YAAa,SAAQ,MAAM,CAAC,QAAQ;IAEzB;IACA;IAFpB,YACoB,QAAgB,EAChB,gBAAiD;QAEjE,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,gBAAgB,CAAC,CAAC;QAHjC,aAAQ,GAAR,QAAQ,CAAQ;QAChB,qBAAgB,GAAhB,gBAAgB,CAAiC;QAIjE,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC;QACxB,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;QAC5B,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC;QAC3B,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC;IAC1C,CAAC;CACJ;AAZD,oCAYC;AAED;;GAEG;AACH,MAAa,yBAAyB;IAKb;IACA;IALb,oBAAoB,GAAmE,IAAI,MAAM,CAAC,YAAY,EAA6C,CAAC;IAC3J,mBAAmB,GAA4D,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;IAExH,YACqB,aAA6B,EAC7B,cAA+B;QAD/B,kBAAa,GAAb,aAAa,CAAgB;QAC7B,mBAAc,GAAd,cAAc,CAAiB;IACjD,CAAC;IAEJ;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,CAAC;IACrC,CAAC;IAED;;;;OAIG;IACH,WAAW,CAAC,OAAwB;QAChC,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,WAAW,CAAC,OAAyB;QACvC,IAAI,CAAC;YACD,IAAI,CAAC,OAAO,EAAE,CAAC;gBACX,4CAA4C;gBAC5C,OAAO,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;YACrC,CAAC;iBAAM,IAAI,OAAO,YAAY,YAAY,EAAE,CAAC;gBACzC,sDAAsD;gBACtD,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YACvD,CAAC;iBAAM,IAAI,OAAO,YAAY,qBAAqB,EAAE,CAAC;gBAClD,oEAAoE;gBACpE,OAAO,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAC1D,CAAC;iBAAM,CAAC;gBACJ,sCAAsC;gBACtC,OAAO,EAAE,CAAC;YACd,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,6BAA6B,KAAK,EAAE,CAAC,CAAC;YAC/D,OAAO,EAAE,CAAC;QACd,CAAC;IACL,CAAC;IAED;;;OAGG;IACK,KAAK,CAAC,YAAY;QACtB,IAAI,CAAC;YACD,6CAA6C;YAC7C,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,eAAe,EAAE,CAAC;YAE9D,kCAAkC;YAClC,OAAO,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,YAAY,CAC7C,QAAQ,EACR,MAAM,CAAC,wBAAwB,CAAC,SAAS,CAC5C,CAAC,CAAC;QACP,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,6BAA6B,KAAK,EAAE,CAAC,CAAC;YAC/D,OAAO,EAAE,CAAC;QACd,CAAC;IACL,CAAC;IAED;;;;OAIG;IACK,KAAK,CAAC,cAAc,CAAC,QAAgB;QACzC,IAAI,CAAC;YACD,2BAA2B;YAC3B,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YAErE,oCAAoC;YACpC,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;gBACxB,wEAAwE;gBACxE,MAAM,qBAAqB,GAAG,MAAM,CAAC,kBAAkB,IAAI,MAAM,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC;gBAChG,MAAM,gBAAgB,GAAG,qBAAqB;oBAC1C,CAAC,CAAC,MAAM,CAAC,wBAAwB,CAAC,SAAS;oBAC3C,CAAC,CAAC,MAAM,CAAC,wBAAwB,CAAC,IAAI,CAAC;gBAE3C,OAAO,IAAI,qBAAqB,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;YAC/D,CAAC,CAAC,CAAC;QACP,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,+BAA+B,KAAK,EAAE,CAAC,CAAC;YACjE,OAAO,EAAE,CAAC;QACd,CAAC;IACL,CAAC;IAED;;;;OAIG;IACK,yBAAyB,CAAC,MAAiC;QAC/D,IAAI,CAAC,MAAM,CAAC,kBAAkB,IAAI,MAAM,CAAC,kBAAkB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvE,OAAO,EAAE,CAAC;QACd,CAAC;QAED,OAAO,MAAM,CAAC,kBAAkB,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAC7C,IAAI,yBAAyB,CAAC,SAAS,EAAE,MAAM,CAAC,EAAE,CAAC,CACtD,CAAC;IACN,CAAC;CACJ;AA/GD,8DA+GC"}