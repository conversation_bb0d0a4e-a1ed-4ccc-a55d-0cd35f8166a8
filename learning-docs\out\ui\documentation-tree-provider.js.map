{"version": 3, "file": "documentation-tree-provider.js", "sourceRoot": "", "sources": ["../../src/ui/documentation-tree-provider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,2CAA6B;AAK7B;;GAEG;AACH,MAAa,qBAAsB,SAAQ,MAAM,CAAC,QAAQ;IAElC;IACA;IAFpB,YACoB,MAAiC,EACjC,gBAAiD;QAEjE,KAAK,CAAC,qBAAqB,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,gBAAgB,CAAC,CAAC;QAHhD,WAAM,GAAN,MAAM,CAA2B;QACjC,qBAAgB,GAAhB,gBAAgB,CAAiC;QAIjE,IAAI,CAAC,OAAO,GAAG,GAAG,MAAM,CAAC,QAAQ,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;QAC1D,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC,SAAS,CAAC;QACpC,IAAI,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;QAEpB,uCAAuC;QACvC,QAAQ,MAAM,CAAC,SAAS,EAAE,CAAC;YACvB,KAAK,QAAQ;gBACT,IAAI,CAAC,QAAQ,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBAC5C,MAAM;YACV,KAAK,QAAQ;gBACT,IAAI,CAAC,QAAQ,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;gBAC7C,MAAM;YACV,KAAK,QAAQ;gBACT,IAAI,CAAC,QAAQ,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;gBAC9C,MAAM;YACV,KAAK,UAAU;gBACX,IAAI,CAAC,QAAQ,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;gBAC7C,MAAM;QACd,CAAC;QAED,kDAAkD;QAClD,IAAI,CAAC,OAAO,GAAG;YACX,OAAO,EAAE,gCAAgC;YACzC,KAAK,EAAE,oBAAoB;YAC3B,SAAS,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC;SACzB,CAAC;IACN,CAAC;IAED;;;;OAIG;IACK,MAAM,CAAC,QAAQ,CAAC,MAAiC;QACrD,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAEhD,QAAQ,MAAM,CAAC,SAAS,EAAE,CAAC;YACvB,KAAK,QAAQ;gBACT,OAAO,WAAW,QAAQ,EAAE,CAAC;YACjC,KAAK,QAAQ;gBACT,OAAO,YAAY,QAAQ,EAAE,CAAC;YAClC,KAAK,QAAQ;gBACT,OAAO,WAAW,QAAQ,EAAE,CAAC;YACjC,KAAK,UAAU;gBACX,OAAO,YAAY,QAAQ,EAAE,CAAC;YAClC;gBACI,OAAO,QAAQ,CAAC;QACxB,CAAC;IACL,CAAC;CACJ;AAxDD,sDAwDC;AAED;;GAEG;AACH,MAAa,YAAa,SAAQ,MAAM,CAAC,QAAQ;IAEzB;IACA;IAFpB,YACoB,QAAgB,EAChB,gBAAiD;QAEjE,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,gBAAgB,CAAC,CAAC;QAHjC,aAAQ,GAAR,QAAQ,CAAQ;QAChB,qBAAgB,GAAhB,gBAAgB,CAAiC;QAIjE,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC;QACxB,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;QAC5B,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC;QAC3B,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC;IAC1C,CAAC;CACJ;AAZD,oCAYC;AAED;;GAEG;AACH,MAAa,yBAAyB;IAKb;IACA;IALb,oBAAoB,GAAmE,IAAI,MAAM,CAAC,YAAY,EAA6C,CAAC;IAC3J,mBAAmB,GAA4D,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;IAExH,YACqB,aAA6B,EAC7B,cAA+B;QAD/B,kBAAa,GAAb,aAAa,CAAgB;QAC7B,mBAAc,GAAd,cAAc,CAAiB;IACjD,CAAC;IAEJ;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,CAAC;IACrC,CAAC;IAED;;;;OAIG;IACH,WAAW,CAAC,OAAwB;QAChC,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,WAAW,CAAC,OAAyB;QACvC,IAAI,CAAC;YACD,IAAI,CAAC,OAAO,EAAE,CAAC;gBACX,4CAA4C;gBAC5C,OAAO,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;YACrC,CAAC;iBAAM,IAAI,OAAO,YAAY,YAAY,EAAE,CAAC;gBACzC,sDAAsD;gBACtD,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YACvD,CAAC;iBAAM,CAAC;gBACJ,wCAAwC;gBACxC,OAAO,EAAE,CAAC;YACd,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,6BAA6B,KAAK,EAAE,CAAC,CAAC;YAC/D,OAAO,EAAE,CAAC;QACd,CAAC;IACL,CAAC;IAED;;;OAGG;IACK,KAAK,CAAC,YAAY;QACtB,IAAI,CAAC;YACD,6CAA6C;YAC7C,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,eAAe,EAAE,CAAC;YAE9D,kCAAkC;YAClC,OAAO,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,YAAY,CAC7C,QAAQ,EACR,MAAM,CAAC,wBAAwB,CAAC,SAAS,CAC5C,CAAC,CAAC;QACP,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,6BAA6B,KAAK,EAAE,CAAC,CAAC;YAC/D,OAAO,EAAE,CAAC;QACd,CAAC;IACL,CAAC;IAED;;;;OAIG;IACK,KAAK,CAAC,cAAc,CAAC,QAAgB;QACzC,IAAI,CAAC;YACD,2BAA2B;YAC3B,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YAErE,oCAAoC;YACpC,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,qBAAqB,CAClD,MAAM,EACN,MAAM,CAAC,wBAAwB,CAAC,IAAI,CACvC,CAAC,CAAC;QACP,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,+BAA+B,KAAK,EAAE,CAAC,CAAC;YACjE,OAAO,EAAE,CAAC;QACd,CAAC;IACL,CAAC;CACJ;AAxFD,8DAwFC"}