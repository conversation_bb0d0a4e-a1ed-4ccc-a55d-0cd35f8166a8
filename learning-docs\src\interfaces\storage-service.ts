import { ChangeDocumentationRecord } from './storage';

/**
 * Interface for storage service.
 */
export interface IStorageService {
    /**
     * Save a documentation record.
     * @param record The record to save.
     */
    saveDocumentationRecord(record: ChangeDocumentationRecord): Promise<void>;

    /**
     * Get a record by its ID.
     * @param id The ID of the record to retrieve.
     * @returns The record, or undefined if not found.
     */
    getRecordById(id: string): Promise<ChangeDocumentationRecord | undefined>;

    /**
     * Get all records for a specific file.
     * @param filePath The path to the file.
     * @returns An array of records for the file.
     */
    getRecordsByFile(filePath: string): Promise<ChangeDocumentationRecord[]>;

    /**
     * Get all file paths that have documentation records.
     * @returns An array of file paths.
     */
    getAllFilePaths(): Promise<string[]>;
}
