"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIDocumentationService = void 0;
const path = __importStar(require("path"));
const https = __importStar(require("https"));
const http = __importStar(require("http"));
/**
 * Implementation of the AI documentation service.
 */
class AIDocumentationService {
    loggerService;
    configService;
    constructor(loggerService, configService) {
        this.loggerService = loggerService;
        this.configService = configService;
    }
    /**
     * Generate AI documentation for a change.
     *
     * @param record The change documentation record.
     * @returns A promise that resolves to the AI analysis, or null if generation is not possible.
     */
    async generateDocumentation(record) {
        try {
            this.loggerService.info(`Generating AI documentation for: ${record.filePath}`);
            // Check if AI documentation is enabled
            if (!this.configService.isFeatureEnabled('aiDocumentation')) {
                this.loggerService.info('AI documentation is disabled');
                return null;
            }
            // Get the FastAgent URL from configuration
            const fastAgentUrl = this.configService.getSetting('fastAgentUrl');
            if (!fastAgentUrl) {
                this.loggerService.warn('FastAgent URL is not configured');
                return null;
            }
            // Prepare the request payload
            const payload = {
                filePath: record.filePath,
                eventType: record.eventType,
                previousContent: record.previousContent || '',
                currentContent: record.currentContent || '',
                rawDiff: record.rawDiff || '',
                semanticPrimitives: record.semanticPrimitives || [],
                contextualInfo: record.contextualInfo || {}
            };
            // Send the request to the FastAgent
            const aiAnalysis = await this.sendRequestToFastAgent(fastAgentUrl, payload);
            if (aiAnalysis) {
                this.loggerService.info(`AI documentation generated for: ${record.filePath}`);
                return aiAnalysis;
            }
            else {
                this.loggerService.warn(`Failed to generate AI documentation for: ${record.filePath}`);
                return null;
            }
        }
        catch (error) {
            this.loggerService.error(`Error generating AI documentation: ${error}`);
            return null;
        }
    }
    /**
     * Send a request to the FastAgent.
     * @param url The URL of the FastAgent.
     * @param payload The payload to send.
     * @returns A promise that resolves to the AI analysis, or null if the request fails.
     */
    async sendRequestToFastAgent(url, payload) {
        return new Promise((resolve, reject) => {
            // Determine if we're using http or https
            const client = url.startsWith('https') ? https : http;
            // Prepare the request options
            const requestOptions = {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            };
            // Create the request
            const req = client.request(url, requestOptions, (res) => {
                let data = '';
                // Collect the response data
                res.on('data', (chunk) => {
                    data += chunk;
                });
                // Process the response when it's complete
                res.on('end', () => {
                    if (res.statusCode && res.statusCode >= 200 && res.statusCode < 300) {
                        try {
                            const aiAnalysis = JSON.parse(data);
                            resolve(aiAnalysis);
                        }
                        catch (error) {
                            this.loggerService.error(`Error parsing FastAgent response: ${error}`);
                            resolve(null);
                        }
                    }
                    else {
                        this.loggerService.error(`FastAgent returned status code: ${res.statusCode}`);
                        resolve(null);
                    }
                });
            });
            // Handle request errors
            req.on('error', (error) => {
                this.loggerService.error(`Error sending request to FastAgent: ${error}`);
                resolve(null);
            });
            // Send the payload
            req.write(JSON.stringify(payload));
            req.end();
        });
    }
    /**
     * Generate a fallback AI analysis when the FastAgent is not available.
     * @param record The change documentation record.
     * @returns An AI analysis with basic information.
     */
    generateFallbackAnalysis(record) {
        const fileExt = path.extname(record.filePath);
        const fileName = path.basename(record.filePath);
        let summary = '';
        let explanation = '';
        switch (record.eventType) {
            case 'create':
                summary = `Created a new ${fileExt} file: ${fileName}`;
                explanation = `This change introduces a new file named ${fileName} to the codebase.`;
                break;
            case 'modify':
                summary = `Modified ${fileName}`;
                explanation = `This change updates the existing file ${fileName}.`;
                break;
            case 'delete':
                summary = `Deleted ${fileName}`;
                explanation = `This change removes the file ${fileName} from the codebase.`;
                break;
        }
        // Add information about semantic primitives if available
        if (record.semanticPrimitives && record.semanticPrimitives.length > 0) {
            const addedPrimitives = record.semanticPrimitives.filter(p => p.changeType === 'added');
            const removedPrimitives = record.semanticPrimitives.filter(p => p.changeType === 'removed');
            const modifiedPrimitives = record.semanticPrimitives.filter(p => p.changeType === 'modified');
            if (addedPrimitives.length > 0) {
                explanation += `\n\nAdded ${addedPrimitives.length} new elements:`;
                addedPrimitives.forEach(p => {
                    explanation += `\n- ${p.type} ${p.name}`;
                });
            }
            if (removedPrimitives.length > 0) {
                explanation += `\n\nRemoved ${removedPrimitives.length} elements:`;
                removedPrimitives.forEach(p => {
                    explanation += `\n- ${p.type} ${p.name}`;
                });
            }
            if (modifiedPrimitives.length > 0) {
                explanation += `\n\nModified ${modifiedPrimitives.length} elements:`;
                modifiedPrimitives.forEach(p => {
                    explanation += `\n- ${p.type} ${p.name}`;
                });
            }
        }
        return {
            summary,
            explanation,
            impact: 'Impact analysis requires the FastAgent to be available.',
            suggestions: [
                'Configure the FastAgent URL in the extension settings to get more detailed analysis.'
            ],
            learningResources: [
                {
                    title: 'VSCode Extension Documentation',
                    url: 'https://code.visualstudio.com/api',
                    description: 'Official documentation for VSCode extensions.'
                }
            ]
        };
    }
}
exports.AIDocumentationService = AIDocumentationService;
//# sourceMappingURL=ai-documentation-service.js.map