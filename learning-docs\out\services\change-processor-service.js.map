{"version": 3, "file": "change-processor-service.js", "sourceRoot": "", "sources": ["../../src/services/change-processor-service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,2CAA6B;AAc7B;;GAEG;AACH,MAAa,sBAAsB;IAIV;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAXb,WAAW,GAAwB,EAAE,CAAC;IAE9C,YACqB,aAA6B,EAC7B,qBAAoD,EACpD,cAA+B,EAC/B,kBAAwC,EACxC,WAA0B,EAC1B,uBAAkD,EAClD,sBAAgD,EAChD,sBAAgD,EAChD,aAA8B;QAR9B,kBAAa,GAAb,aAAa,CAAgB;QAC7B,0BAAqB,GAArB,qBAAqB,CAA+B;QACpD,mBAAc,GAAd,cAAc,CAAiB;QAC/B,uBAAkB,GAAlB,kBAAkB,CAAsB;QACxC,gBAAW,GAAX,WAAW,CAAe;QAC1B,4BAAuB,GAAvB,uBAAuB,CAA2B;QAClD,2BAAsB,GAAtB,sBAAsB,CAA0B;QAChD,2BAAsB,GAAtB,sBAAsB,CAA0B;QAChD,kBAAa,GAAb,aAAa,CAAiB;IAChD,CAAC;IAEJ;;OAEG;IACI,KAAK;QACR,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QAErD,oCAAoC;QACpC,IAAI,CAAC,qBAAqB,CAAC,cAAc,EAAE,CAAC;QAE5C,oCAAoC;QACpC,IAAI,CAAC,WAAW,CAAC,IAAI,CACjB,IAAI,CAAC,qBAAqB,CAAC,iBAAiB,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAC9E,CAAC;IACN,CAAC;IAED;;OAEG;IACI,IAAI;QACP,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QAErD,mCAAmC;QACnC,IAAI,CAAC,qBAAqB,CAAC,aAAa,EAAE,CAAC;QAE3C,0BAA0B;QAC1B,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACxC,UAAU,CAAC,OAAO,EAAE,CAAC;QACzB,CAAC;QACD,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;IAC1B,CAAC;IAED;;;OAGG;IACK,KAAK,CAAC,aAAa,CAAC,MAAwB;QAChD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,sBAAsB,MAAM,CAAC,SAAS,MAAM,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;QAEvF,IAAI,CAAC;YACD,gDAAgD;YAChD,MAAM,MAAM,GAA8B;gBACtC,GAAG,MAAM;aACZ,CAAC;YAEF,gDAAgD;YAChD,IAAI,MAAM,CAAC,SAAS,KAAK,QAAQ,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC3D,MAAM,CAAC,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAC/F,CAAC;YAED,6DAA6D;YAC7D,IAAI,IAAI,CAAC,WAAW;gBAChB,CAAC,MAAM,CAAC,SAAS,KAAK,QAAQ,IAAI,MAAM,CAAC,SAAS,KAAK,QAAQ,IAAI,MAAM,CAAC,SAAS,KAAK,UAAU,CAAC;gBACnG,MAAM,CAAC,cAAc,EAAE,CAAC;gBAExB,MAAM,eAAe,GAAG,MAAM,CAAC,eAAe,IAAI,EAAE,CAAC;gBACrD,MAAM,cAAc,GAAG,MAAM,CAAC,cAAc,CAAC;gBAE7C,wBAAwB;gBACxB,MAAM,CAAC,OAAO,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,mBAAmB,CACvD,eAAe,EACf,cAAc,EACd,MAAM,CAAC,QAAQ,EACf,MAAM,CAAC,QAAQ,CAClB,CAAC;YACN,CAAC;YAED,uCAAuC;YACvC,IAAI,IAAI,CAAC,uBAAuB;gBAC5B,IAAI,CAAC,aAAa,EAAE,gBAAgB,CAAC,kBAAkB,CAAC;gBACxD,CAAC,MAAM,CAAC,SAAS,KAAK,QAAQ,IAAI,MAAM,CAAC,SAAS,KAAK,QAAQ,IAAI,MAAM,CAAC,SAAS,KAAK,UAAU,CAAC;gBACnG,MAAM,CAAC,cAAc,EAAE,CAAC;gBAExB,IAAI,CAAC;oBACD,4CAA4C;oBAC5C,MAAM,UAAU,GAAG,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;oBAEnE,0BAA0B;oBAC1B,MAAM,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC;oBAClE,MAAM,OAAO,GAAG,aAAa,CAAC,CAAC;wBAC3B,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,aAAa,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;wBACrD,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;oBAErC,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,mBAAmB,CAC3E,MAAM,CAAC,eAAe,EACtB,MAAM,CAAC,cAAc,EACrB,OAAO,EACP,UAAU,CACb,CAAC;oBAEF,IAAI,gBAAgB,EAAE,CAAC;wBACnB,MAAM,CAAC,kBAAkB,GAAG,gBAAgB,CAAC,UAAU,CAAC;wBACxD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,mCAAmC,MAAM,CAAC,QAAQ,KAAK,gBAAgB,CAAC,UAAU,CAAC,MAAM,mBAAmB,CAAC,CAAC;oBAC1I,CAAC;gBACL,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACb,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,6BAA6B,KAAK,EAAE,CAAC,CAAC;gBACnE,CAAC;YACL,CAAC;YAED,yCAAyC;YACzC,IAAI,IAAI,CAAC,sBAAsB;gBAC3B,IAAI,CAAC,aAAa,EAAE,gBAAgB,CAAC,oBAAoB,CAAC;gBAC1D,CAAC,MAAM,CAAC,SAAS,KAAK,QAAQ,IAAI,MAAM,CAAC,SAAS,KAAK,QAAQ,IAAI,MAAM,CAAC,SAAS,KAAK,UAAU,CAAC,EAAE,CAAC;gBAEtG,IAAI,CAAC;oBACD,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,iBAAiB,CACtE,MAAM,CAAC,QAAQ,EACf,MAAM,CAAC,kBAAkB,CAC5B,CAAC;oBAEF,IAAI,cAAc,EAAE,CAAC;wBACjB,MAAM,CAAC,cAAc,GAAG,cAAc,CAAC;wBACvC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,qCAAqC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;oBACpF,CAAC;gBACL,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACb,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,+BAA+B,KAAK,EAAE,CAAC,CAAC;gBACrE,CAAC;YACL,CAAC;YAED,uCAAuC;YACvC,IAAI,IAAI,CAAC,sBAAsB;gBAC3B,IAAI,CAAC,aAAa,EAAE,gBAAgB,CAAC,iBAAiB,CAAC;gBACvD,CAAC,MAAM,CAAC,SAAS,KAAK,QAAQ,IAAI,MAAM,CAAC,SAAS,KAAK,QAAQ,IAAI,MAAM,CAAC,SAAS,KAAK,UAAU,CAAC,EAAE,CAAC;gBAEtG,IAAI,CAAC;oBACD,qFAAqF;oBACrF,MAAM,UAAU,GAAG,EAAE,GAAG,MAAM,EAAE,CAAC;oBAEjC,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;oBAEvF,IAAI,UAAU,EAAE,CAAC;wBACb,MAAM,CAAC,UAAU,GAAG,UAAU,CAAC;wBAC/B,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,kCAAkC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;oBACjF,CAAC;gBACL,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACb,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,uCAAuC,KAAK,EAAE,CAAC,CAAC;gBAC7E,CAAC;YACL,CAAC;YAED,kBAAkB;YAClB,MAAM,IAAI,CAAC,cAAc,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC;YAE1D,yDAAyD;YACzD,IAAI,IAAI,CAAC,kBAAkB;gBACvB,CAAC,MAAM,CAAC,SAAS,KAAK,QAAQ,IAAI,MAAM,CAAC,SAAS,KAAK,QAAQ,IAAI,MAAM,CAAC,SAAS,KAAK,UAAU,CAAC;gBACnG,MAAM,CAAC,cAAc,EAAE,CAAC;gBACxB,MAAM,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,cAAc,CAAC,CAAC;YACtF,CAAC;YAED,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,kCAAkC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;QAC3E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,6BAA6B,KAAK,EAAE,CAAC,CAAC;QACnE,CAAC;IACL,CAAC;IAED;;;;OAIG;IACK,yBAAyB,CAAC,QAAgB;QAC9C,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;QACvD,MAAM,sBAAsB,GAA2B;YACnD,KAAK,EAAE,YAAY;YACnB,KAAK,EAAE,YAAY;YACnB,MAAM,EAAE,iBAAiB;YACzB,MAAM,EAAE,iBAAiB;YACzB,KAAK,EAAE,QAAQ;YACf,OAAO,EAAE,MAAM;YACf,KAAK,EAAE,QAAQ;YACf,KAAK,EAAE,IAAI;YACX,KAAK,EAAE,MAAM;YACb,MAAM,EAAE,KAAK;YACb,OAAO,EAAE,MAAM;YACf,MAAM,EAAE,KAAK;YACb,OAAO,EAAE,MAAM;YACf,KAAK,EAAE,UAAU;YACjB,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,GAAG;YACT,IAAI,EAAE,GAAG;YACT,MAAM,EAAE,KAAK;SAChB,CAAC;QAEF,OAAO,sBAAsB,CAAC,SAAS,CAAC,IAAI,WAAW,CAAC;IAC5D,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,IAAI,EAAE,CAAC;IAChB,CAAC;CACJ;AA9MD,wDA8MC"}