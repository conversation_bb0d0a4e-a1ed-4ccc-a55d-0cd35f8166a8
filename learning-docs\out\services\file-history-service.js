"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileHistoryService = void 0;
const path = __importStar(require("path"));
const fs = __importStar(require("fs"));
const crypto = __importStar(require("crypto"));
const child_process = __importStar(require("child_process"));
const util_1 = require("util");
const execFile = (0, util_1.promisify)(child_process.execFile);
/**
 * Implementation of the file history service.
 */
class FileHistoryService {
    context;
    loggerService;
    workspaceService;
    inMemoryCache = new Map();
    cacheDir;
    constructor(context, loggerService, workspaceService) {
        this.context = context;
        this.loggerService = loggerService;
        this.workspaceService = workspaceService;
        // Create cache directory within the extension's global storage
        this.cacheDir = path.join(context.globalStorageUri.fsPath, '.learningDocsCache');
        this.ensureCacheDirectoryExists();
    }
    /**
     * Get the previous version of a file.
     * @param filePath The path to the file.
     * @returns A promise that resolves to the previous content, or undefined if not found.
     */
    async getPreviousVersion(filePath) {
        this.loggerService.info(`Getting previous version for: ${filePath}`);
        // Try in-memory cache first (fastest)
        if (this.inMemoryCache.has(filePath)) {
            this.loggerService.info(`Found previous version in memory cache for: ${filePath}`);
            return this.inMemoryCache.get(filePath);
        }
        // Try Git next
        try {
            const gitContent = await this.getFromGit(filePath);
            if (gitContent) {
                this.loggerService.info(`Found previous version in Git for: ${filePath}`);
                return gitContent;
            }
        }
        catch (error) {
            this.loggerService.warn(`Failed to get previous version from Git: ${error}`);
        }
        // Try persistent cache
        try {
            const cacheContent = await this.getFromPersistentCache(filePath);
            if (cacheContent) {
                this.loggerService.info(`Found previous version in persistent cache for: ${filePath}`);
                return cacheContent;
            }
        }
        catch (error) {
            this.loggerService.warn(`Failed to get previous version from persistent cache: ${error}`);
        }
        this.loggerService.warn(`No previous version found for: ${filePath}`);
        return undefined;
    }
    /**
     * Update the cache with the current content of a file.
     * @param filePath The path to the file.
     * @param content The current content of the file.
     */
    async updateCache(filePath, content) {
        // Update in-memory cache
        this.inMemoryCache.set(filePath, content);
        // Update persistent cache
        try {
            await this.updatePersistentCache(filePath, content);
        }
        catch (error) {
            this.loggerService.error(`Failed to update persistent cache: ${error}`);
        }
    }
    /**
     * Get the previous version of a file from Git.
     * @param filePath The path to the file.
     * @returns A promise that resolves to the content from Git, or undefined if not found.
     */
    async getFromGit(filePath) {
        const workspaceRoot = this.workspaceService.getWorkspaceRoot();
        if (!workspaceRoot) {
            return undefined;
        }
        try {
            // Check if the file is in a Git repository
            const { stdout: gitRootOutput } = await execFile('git', ['rev-parse', '--show-toplevel'], {
                cwd: workspaceRoot.fsPath
            });
            if (!gitRootOutput.trim()) {
                return undefined;
            }
            // Get the relative path from the Git root
            const gitRoot = gitRootOutput.trim();
            const relativePath = path.relative(gitRoot, path.join(workspaceRoot.fsPath, filePath));
            // Get the content from the HEAD commit
            const { stdout: gitContent } = await execFile('git', ['show', `HEAD:${relativePath}`], {
                cwd: workspaceRoot.fsPath
            });
            return gitContent;
        }
        catch (error) {
            // File might not be in Git yet, or other Git error
            return undefined;
        }
    }
    /**
     * Get the previous version of a file from the persistent cache.
     * @param filePath The path to the file.
     * @returns A promise that resolves to the content from the cache, or undefined if not found.
     */
    async getFromPersistentCache(filePath) {
        const workspaceId = this.getWorkspaceId();
        const fileHash = this.hashFilePath(filePath);
        const cachePath = path.join(this.cacheDir, workspaceId, fileHash);
        if (!fs.existsSync(cachePath)) {
            return undefined;
        }
        // Get all cache files for this file path
        const cacheFiles = await fs.promises.readdir(cachePath);
        if (cacheFiles.length === 0) {
            return undefined;
        }
        // Sort by timestamp (newest first)
        cacheFiles.sort((a, b) => {
            const timestampA = parseInt(a.split('_')[0], 10);
            const timestampB = parseInt(b.split('_')[0], 10);
            return timestampB - timestampA;
        });
        // Get the most recent cache file
        const mostRecentFile = cacheFiles[0];
        const content = await fs.promises.readFile(path.join(cachePath, mostRecentFile), 'utf-8');
        return content;
    }
    /**
     * Update the persistent cache with the current content of a file.
     * @param filePath The path to the file.
     * @param content The current content of the file.
     */
    async updatePersistentCache(filePath, content) {
        const workspaceId = this.getWorkspaceId();
        const fileHash = this.hashFilePath(filePath);
        const cachePath = path.join(this.cacheDir, workspaceId, fileHash);
        // Ensure the cache directory exists
        if (!fs.existsSync(cachePath)) {
            fs.mkdirSync(cachePath, { recursive: true });
        }
        // Create a cache file with timestamp
        const timestamp = Date.now();
        const cacheFile = path.join(cachePath, `${timestamp}_${path.basename(filePath)}`);
        await fs.promises.writeFile(cacheFile, content);
        // Prune old cache files (keep only the 5 most recent)
        await this.pruneCache(cachePath);
    }
    /**
     * Prune old cache files, keeping only the most recent ones.
     * @param cachePath The path to the cache directory.
     */
    async pruneCache(cachePath) {
        const maxCacheFiles = 5;
        const cacheFiles = await fs.promises.readdir(cachePath);
        if (cacheFiles.length <= maxCacheFiles) {
            return;
        }
        // Sort by timestamp (newest first)
        cacheFiles.sort((a, b) => {
            const timestampA = parseInt(a.split('_')[0], 10);
            const timestampB = parseInt(b.split('_')[0], 10);
            return timestampB - timestampA;
        });
        // Delete older files
        for (let i = maxCacheFiles; i < cacheFiles.length; i++) {
            await fs.promises.unlink(path.join(cachePath, cacheFiles[i]));
        }
    }
    /**
     * Ensure the cache directory exists.
     */
    ensureCacheDirectoryExists() {
        if (!fs.existsSync(this.cacheDir)) {
            fs.mkdirSync(this.cacheDir, { recursive: true });
        }
    }
    /**
     * Get a unique identifier for the current workspace.
     * @returns A string identifier for the workspace.
     */
    getWorkspaceId() {
        const workspaceRoot = this.workspaceService.getWorkspaceRoot();
        if (!workspaceRoot) {
            return 'no-workspace';
        }
        // Use the last segment of the workspace path as an identifier
        return path.basename(workspaceRoot.fsPath);
    }
    /**
     * Hash a file path to create a safe directory name.
     * @param filePath The file path to hash.
     * @returns A hash of the file path.
     */
    hashFilePath(filePath) {
        return crypto.createHash('md5').update(filePath).digest('hex');
    }
}
exports.FileHistoryService = FileHistoryService;
//# sourceMappingURL=file-history-service.js.map