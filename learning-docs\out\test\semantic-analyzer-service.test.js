"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const assert = __importStar(require("assert"));
const vscode = __importStar(require("vscode"));
const semantic_analyzer_service_1 = require("../services/semantic-analyzer-service");
const logger_service_1 = require("../services/logger-service");
const config_service_1 = require("../services/config-service");
const workspace_service_1 = require("../services/workspace-service");
suite('SemanticAnalyzerService Test Suite', () => {
    let semanticAnalyzer;
    let loggerService;
    let configService;
    let workspaceService;
    // Mock document symbols for testing
    const createMockSymbol = (name, kind, range, detail, children) => ({
        name,
        kind,
        range,
        selectionRange: range,
        detail: detail || '',
        children: children || []
    });
    const mockRange = new vscode.Range(0, 0, 5, 0);
    const mockUri = vscode.Uri.file('/test/file.ts');
    suiteSetup(() => {
        loggerService = new logger_service_1.LoggerService();
        configService = new config_service_1.ConfigService();
        workspaceService = new workspace_service_1.WorkspaceService(configService, loggerService);
        semanticAnalyzer = new semantic_analyzer_service_1.SemanticAnalyzerService(loggerService, configService, workspaceService);
    });
    test('should return null when semantic analysis is disabled', async () => {
        // Mock config to disable semantic analysis
        const originalGetSetting = configService.getSetting;
        configService.getSetting = (key) => {
            if (key === 'enable.semanticAnalysis') {
                return false;
            }
            return originalGetSetting.call(configService, key);
        };
        const result = await semanticAnalyzer.getSemanticAnalysis(undefined, 'const x = 1;', mockUri, 'typescript');
        assert.strictEqual(result, null);
        // Restore original method
        configService.getSetting = originalGetSetting;
    });
    test('should return null for unsupported language', async () => {
        const result = await semanticAnalyzer.getSemanticAnalysis(undefined, 'some content', mockUri, 'unsupported-language');
        assert.strictEqual(result, null);
    });
    test('should detect added function', async () => {
        // Mock vscode.commands.executeCommand to return symbols
        const originalExecuteCommand = vscode.commands.executeCommand;
        let callCount = 0;
        vscode.commands.executeCommand = async (command, ...args) => {
            if (command === 'vscode.executeDocumentSymbolProvider') {
                callCount++;
                if (callCount === 1) {
                    // First call (current content) - return function symbol
                    return [createMockSymbol('testFunction', vscode.SymbolKind.Function, mockRange, 'function testFunction(): void')];
                }
                // Previous content had no symbols
                return [];
            }
            return originalExecuteCommand(command, ...args);
        };
        // Mock workspace.openTextDocument
        const originalOpenTextDocument = vscode.workspace.openTextDocument;
        vscode.workspace.openTextDocument = async (options) => {
            return {
                uri: mockUri,
                getText: () => options.content || ''
            };
        };
        const result = await semanticAnalyzer.getSemanticAnalysis('', // previous content (empty)
        'function testFunction() {}', // current content
        mockUri, 'typescript');
        assert.notStrictEqual(result, null);
        assert.strictEqual(result.primitives.length, 1);
        assert.strictEqual(result.primitives[0].operation, 'add');
        assert.strictEqual(result.primitives[0].elementType, 'function');
        assert.strictEqual(result.primitives[0].elementName, 'testFunction');
        // Restore original methods
        vscode.commands.executeCommand = originalExecuteCommand;
        vscode.workspace.openTextDocument = originalOpenTextDocument;
    });
    test('should detect removed function', async () => {
        const originalExecuteCommand = vscode.commands.executeCommand;
        let callCount = 0;
        vscode.commands.executeCommand = async (command, ...args) => {
            if (command === 'vscode.executeDocumentSymbolProvider') {
                callCount++;
                if (callCount === 1) {
                    // First call (current content) - no symbols
                    return [];
                }
                else {
                    // Second call (previous content) - had function symbol
                    return [createMockSymbol('removedFunction', vscode.SymbolKind.Function, mockRange, 'function removedFunction(): void')];
                }
            }
            return originalExecuteCommand(command, ...args);
        };
        const originalOpenTextDocument = vscode.workspace.openTextDocument;
        vscode.workspace.openTextDocument = async (options) => {
            return {
                uri: mockUri,
                getText: () => options.content || ''
            };
        };
        const result = await semanticAnalyzer.getSemanticAnalysis('function removedFunction() {}', // previous content
        '', // current content (empty)
        mockUri, 'typescript');
        assert.notStrictEqual(result, null);
        assert.strictEqual(result.primitives.length, 1);
        assert.strictEqual(result.primitives[0].operation, 'remove');
        assert.strictEqual(result.primitives[0].elementType, 'function');
        assert.strictEqual(result.primitives[0].elementName, 'removedFunction');
        // Restore original methods
        vscode.commands.executeCommand = originalExecuteCommand;
        vscode.workspace.openTextDocument = originalOpenTextDocument;
    });
    test('should detect modified function', async () => {
        const originalExecuteCommand = vscode.commands.executeCommand;
        let callCount = 0;
        vscode.commands.executeCommand = async (command, ...args) => {
            if (command === 'vscode.executeDocumentSymbolProvider') {
                callCount++;
                if (callCount === 1) {
                    // Current content - modified function
                    return [createMockSymbol('testFunction', vscode.SymbolKind.Function, mockRange, 'function testFunction(param: string): void')];
                }
                else {
                    // Previous content - original function
                    return [createMockSymbol('testFunction', vscode.SymbolKind.Function, mockRange, 'function testFunction(): void')];
                }
            }
            return originalExecuteCommand(command, ...args);
        };
        const originalOpenTextDocument = vscode.workspace.openTextDocument;
        vscode.workspace.openTextDocument = async (options) => {
            return {
                uri: mockUri,
                getText: () => options.content || ''
            };
        };
        const result = await semanticAnalyzer.getSemanticAnalysis('function testFunction() {}', // previous content
        'function testFunction(param: string) {}', // current content
        mockUri, 'typescript');
        assert.notStrictEqual(result, null);
        assert.strictEqual(result.primitives.length, 1);
        assert.strictEqual(result.primitives[0].operation, 'modify');
        assert.strictEqual(result.primitives[0].elementType, 'function');
        assert.strictEqual(result.primitives[0].elementName, 'testFunction');
        // Restore original methods
        vscode.commands.executeCommand = originalExecuteCommand;
        vscode.workspace.openTextDocument = originalOpenTextDocument;
    });
    test('should handle nested symbols (class with methods)', async () => {
        const originalExecuteCommand = vscode.commands.executeCommand;
        vscode.commands.executeCommand = async (command, ...args) => {
            if (command === 'vscode.executeDocumentSymbolProvider') {
                // Return class with method
                const methodSymbol = createMockSymbol('testMethod', vscode.SymbolKind.Method, mockRange, 'testMethod(): void');
                const classSymbol = createMockSymbol('TestClass', vscode.SymbolKind.Class, mockRange, 'class TestClass', [methodSymbol]);
                return [classSymbol];
            }
            return originalExecuteCommand(command, ...args);
        };
        const originalOpenTextDocument = vscode.workspace.openTextDocument;
        vscode.workspace.openTextDocument = async (options) => {
            return {
                uri: mockUri,
                getText: () => options.content || ''
            };
        };
        const result = await semanticAnalyzer.getSemanticAnalysis('', // previous content (empty)
        'class TestClass { testMethod() {} }', // current content
        mockUri, 'typescript');
        assert.notStrictEqual(result, null);
        assert.strictEqual(result.primitives.length, 2); // class + method
        const classPrimitive = result.primitives.find(p => p.elementType === 'class');
        const methodPrimitive = result.primitives.find(p => p.elementType === 'method');
        assert.notStrictEqual(classPrimitive, undefined);
        assert.notStrictEqual(methodPrimitive, undefined);
        assert.strictEqual(classPrimitive.elementName, 'TestClass');
        assert.strictEqual(methodPrimitive.elementName, 'testMethod');
        // Restore original methods
        vscode.commands.executeCommand = originalExecuteCommand;
        vscode.workspace.openTextDocument = originalOpenTextDocument;
    });
    test('should skip analysis for high text similarity', async () => {
        // Mock config to set low similarity threshold
        const originalGetSetting = configService.getSetting;
        configService.getSetting = (key) => {
            if (key === 'semanticAnalysis.skipIfTextSimilarityAbove') {
                return 0.5; // Low threshold
            }
            return originalGetSetting.call(configService, key);
        };
        const result = await semanticAnalyzer.getSemanticAnalysis('const x = 1;', // previous content
        'const x = 1;', // current content (identical)
        mockUri, 'typescript');
        assert.strictEqual(result, null);
        // Restore original method
        configService.getSetting = originalGetSetting;
    });
    test('should calculate summary correctly', async () => {
        const originalExecuteCommand = vscode.commands.executeCommand;
        let callCount = 0;
        vscode.commands.executeCommand = async (command, ...args) => {
            if (command === 'vscode.executeDocumentSymbolProvider') {
                callCount++;
                if (callCount === 1) {
                    // Current content - two functions
                    return [
                        createMockSymbol('function1', vscode.SymbolKind.Function, mockRange),
                        createMockSymbol('function2', vscode.SymbolKind.Function, mockRange)
                    ];
                }
                else {
                    // Previous content - one function
                    return [createMockSymbol('function1', vscode.SymbolKind.Function, mockRange)];
                }
            }
            return originalExecuteCommand(command, ...args);
        };
        const originalOpenTextDocument = vscode.workspace.openTextDocument;
        vscode.workspace.openTextDocument = async (options) => {
            return {
                uri: mockUri,
                getText: () => options.content || ''
            };
        };
        const result = await semanticAnalyzer.getSemanticAnalysis('function function1() {}', 'function function1() {} function function2() {}', mockUri, 'typescript');
        assert.notStrictEqual(result, null);
        assert.notStrictEqual(result.summary, undefined);
        assert.strictEqual(result.summary.added, 1);
        assert.strictEqual(result.summary.removed, 0);
        // Restore original methods
        vscode.commands.executeCommand = originalExecuteCommand;
        vscode.workspace.openTextDocument = originalOpenTextDocument;
    });
});
//# sourceMappingURL=semantic-analyzer-service.test.js.map