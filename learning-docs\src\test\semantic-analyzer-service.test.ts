import * as assert from 'assert';
import * as vscode from 'vscode';
import { SemanticAnalyzerService } from '../services/semantic-analyzer-service';
import { LoggerService } from '../services/logger-service';
import { ConfigService } from '../services/config-service';
import { WorkspaceService } from '../services/workspace-service';

suite('SemanticAnalyzerService Test Suite', () => {
    let semanticAnalyzer: SemanticAnalyzerService;
    let loggerService: LoggerService;
    let configService: ConfigService;
    let workspaceService: WorkspaceService;

    // Mock document symbols for testing
    const createMockSymbol = (
        name: string,
        kind: vscode.SymbolKind,
        range: vscode.Range,
        detail?: string,
        children?: vscode.DocumentSymbol[]
    ): vscode.DocumentSymbol => ({
        name,
        kind,
        range,
        selectionRange: range,
        detail: detail || '',
        children: children || []
    });

    const mockRange = new vscode.Range(0, 0, 5, 0);
    const mockUri = vscode.Uri.file('/test/file.ts');

    suiteSetup(() => {
        loggerService = new LoggerService();
        configService = new ConfigService();
        workspaceService = new WorkspaceService(configService, loggerService);
        semanticAnalyzer = new SemanticAnalyzerService(loggerService, configService, workspaceService);
    });

    test('should return null when semantic analysis is disabled', async () => {
        // Mock config to disable semantic analysis
        const originalGetSetting = configService.getSetting;
        configService.getSetting = <T>(key: string): T | undefined => {
            if (key === 'enable.semanticAnalysis') {
                return false as T;
            }
            return originalGetSetting.call(configService, key) as T | undefined;
        };

        const result = await semanticAnalyzer.getSemanticAnalysis(
            undefined,
            'const x = 1;',
            mockUri,
            'typescript'
        );

        assert.strictEqual(result, null);

        // Restore original method
        configService.getSetting = originalGetSetting;
    });

    test('should return null for unsupported language', async () => {
        const result = await semanticAnalyzer.getSemanticAnalysis(
            undefined,
            'some content',
            mockUri,
            'unsupported-language'
        );

        assert.strictEqual(result, null);
    });

    test('should detect added function', async () => {
        // Mock vscode.commands.executeCommand to return symbols
        const originalExecuteCommand = vscode.commands.executeCommand;
        let callCount = 0;

        vscode.commands.executeCommand = async <T>(command: string, ...args: any[]): Promise<T> => {
            if (command === 'vscode.executeDocumentSymbolProvider') {
                callCount++;
                if (callCount === 1) {
                    // First call (current content) - return function symbol
                    return [createMockSymbol('testFunction', vscode.SymbolKind.Function, mockRange, 'function testFunction(): void')] as T;
                }
                // Previous content had no symbols
                return [] as T;
            }
            return originalExecuteCommand(command, ...args);
        };

        // Mock workspace.openTextDocument
        const originalOpenTextDocument = vscode.workspace.openTextDocument;
        vscode.workspace.openTextDocument = async (options: any) => {
            return {
                uri: mockUri,
                getText: () => options.content || ''
            } as vscode.TextDocument;
        };

        const result = await semanticAnalyzer.getSemanticAnalysis(
            '', // previous content (empty)
            'function testFunction() {}', // current content
            mockUri,
            'typescript'
        );

        assert.notStrictEqual(result, null);
        assert.strictEqual(result!.primitives.length, 1);
        assert.strictEqual(result!.primitives[0].operation, 'add');
        assert.strictEqual(result!.primitives[0].elementType, 'function');
        assert.strictEqual(result!.primitives[0].elementName, 'testFunction');

        // Restore original methods
        vscode.commands.executeCommand = originalExecuteCommand;
        vscode.workspace.openTextDocument = originalOpenTextDocument;
    });

    test('should detect removed function', async () => {
        const originalExecuteCommand = vscode.commands.executeCommand;
        let callCount = 0;

        vscode.commands.executeCommand = async <T>(command: string, ...args: any[]): Promise<T> => {
            if (command === 'vscode.executeDocumentSymbolProvider') {
                callCount++;
                if (callCount === 1) {
                    // First call (current content) - no symbols
                    return [] as T;
                } else {
                    // Second call (previous content) - had function symbol
                    return [createMockSymbol('removedFunction', vscode.SymbolKind.Function, mockRange, 'function removedFunction(): void')] as T;
                }
            }
            return originalExecuteCommand(command, ...args);
        };

        const originalOpenTextDocument = vscode.workspace.openTextDocument;
        vscode.workspace.openTextDocument = async (options: any) => {
            return {
                uri: mockUri,
                getText: () => options.content || ''
            } as vscode.TextDocument;
        };

        const result = await semanticAnalyzer.getSemanticAnalysis(
            'function removedFunction() {}', // previous content
            '', // current content (empty)
            mockUri,
            'typescript'
        );

        assert.notStrictEqual(result, null);
        assert.strictEqual(result!.primitives.length, 1);
        assert.strictEqual(result!.primitives[0].operation, 'remove');
        assert.strictEqual(result!.primitives[0].elementType, 'function');
        assert.strictEqual(result!.primitives[0].elementName, 'removedFunction');

        // Restore original methods
        vscode.commands.executeCommand = originalExecuteCommand;
        vscode.workspace.openTextDocument = originalOpenTextDocument;
    });

    test('should detect modified function', async () => {
        const originalExecuteCommand = vscode.commands.executeCommand;
        let callCount = 0;

        vscode.commands.executeCommand = async <T>(command: string, ...args: any[]): Promise<T> => {
            if (command === 'vscode.executeDocumentSymbolProvider') {
                callCount++;
                if (callCount === 1) {
                    // Current content - modified function
                    return [createMockSymbol('testFunction', vscode.SymbolKind.Function, mockRange, 'function testFunction(param: string): void')] as T;
                } else {
                    // Previous content - original function
                    return [createMockSymbol('testFunction', vscode.SymbolKind.Function, mockRange, 'function testFunction(): void')] as T;
                }
            }
            return originalExecuteCommand(command, ...args);
        };

        const originalOpenTextDocument = vscode.workspace.openTextDocument;
        vscode.workspace.openTextDocument = async (options: any) => {
            return {
                uri: mockUri,
                getText: () => options.content || ''
            } as vscode.TextDocument;
        };

        const result = await semanticAnalyzer.getSemanticAnalysis(
            'function testFunction() {}', // previous content
            'function testFunction(param: string) {}', // current content
            mockUri,
            'typescript'
        );

        assert.notStrictEqual(result, null);
        assert.strictEqual(result!.primitives.length, 1);
        assert.strictEqual(result!.primitives[0].operation, 'modify');
        assert.strictEqual(result!.primitives[0].elementType, 'function');
        assert.strictEqual(result!.primitives[0].elementName, 'testFunction');

        // Restore original methods
        vscode.commands.executeCommand = originalExecuteCommand;
        vscode.workspace.openTextDocument = originalOpenTextDocument;
    });

    test('should handle nested symbols (class with methods)', async () => {
        const originalExecuteCommand = vscode.commands.executeCommand;

        vscode.commands.executeCommand = async <T>(command: string, ...args: any[]): Promise<T> => {
            if (command === 'vscode.executeDocumentSymbolProvider') {
                // Return class with method
                const methodSymbol = createMockSymbol('testMethod', vscode.SymbolKind.Method, mockRange, 'testMethod(): void');
                const classSymbol = createMockSymbol('TestClass', vscode.SymbolKind.Class, mockRange, 'class TestClass', [methodSymbol]);
                return [classSymbol] as T;
            }
            return originalExecuteCommand(command, ...args);
        };

        const originalOpenTextDocument = vscode.workspace.openTextDocument;
        vscode.workspace.openTextDocument = async (options: any) => {
            return {
                uri: mockUri,
                getText: () => options.content || ''
            } as vscode.TextDocument;
        };

        const result = await semanticAnalyzer.getSemanticAnalysis(
            '', // previous content (empty)
            'class TestClass { testMethod() {} }', // current content
            mockUri,
            'typescript'
        );

        assert.notStrictEqual(result, null);
        assert.strictEqual(result!.primitives.length, 2); // class + method

        const classPrimitive = result!.primitives.find(p => p.elementType === 'class');
        const methodPrimitive = result!.primitives.find(p => p.elementType === 'method');

        assert.notStrictEqual(classPrimitive, undefined);
        assert.notStrictEqual(methodPrimitive, undefined);
        assert.strictEqual(classPrimitive!.elementName, 'TestClass');
        assert.strictEqual(methodPrimitive!.elementName, 'testMethod');

        // Restore original methods
        vscode.commands.executeCommand = originalExecuteCommand;
        vscode.workspace.openTextDocument = originalOpenTextDocument;
    });

    test('should skip analysis for high text similarity', async () => {
        // Mock config to set low similarity threshold
        const originalGetSetting = configService.getSetting;
        configService.getSetting = <T>(key: string): T | undefined => {
            if (key === 'semanticAnalysis.skipIfTextSimilarityAbove') {
                return 0.5 as T; // Low threshold
            }
            return originalGetSetting.call(configService, key) as T | undefined;
        };

        const result = await semanticAnalyzer.getSemanticAnalysis(
            'const x = 1;', // previous content
            'const x = 1;', // current content (identical)
            mockUri,
            'typescript'
        );

        assert.strictEqual(result, null);

        // Restore original method
        configService.getSetting = originalGetSetting;
    });

    test('should calculate summary correctly', async () => {
        const originalExecuteCommand = vscode.commands.executeCommand;
        let callCount = 0;

        vscode.commands.executeCommand = async <T>(command: string, ...args: any[]): Promise<T> => {
            if (command === 'vscode.executeDocumentSymbolProvider') {
                callCount++;
                if (callCount === 1) {
                    // Current content - two functions
                    return [
                        createMockSymbol('function1', vscode.SymbolKind.Function, mockRange),
                        createMockSymbol('function2', vscode.SymbolKind.Function, mockRange)
                    ] as T;
                } else {
                    // Previous content - one function
                    return [createMockSymbol('function1', vscode.SymbolKind.Function, mockRange)] as T;
                }
            }
            return originalExecuteCommand(command, ...args);
        };

        const originalOpenTextDocument = vscode.workspace.openTextDocument;
        vscode.workspace.openTextDocument = async (options: any) => {
            return {
                uri: mockUri,
                getText: () => options.content || ''
            } as vscode.TextDocument;
        };

        const result = await semanticAnalyzer.getSemanticAnalysis(
            'function function1() {}',
            'function function1() {} function function2() {}',
            mockUri,
            'typescript'
        );

        assert.notStrictEqual(result, null);
        assert.notStrictEqual(result!.summary, undefined);
        assert.strictEqual(result!.summary!.added, 1);
        assert.strictEqual(result!.summary!.removed, 0);

        // Restore original methods
        vscode.commands.executeCommand = originalExecuteCommand;
        vscode.workspace.openTextDocument = originalOpenTextDocument;
    });
});
