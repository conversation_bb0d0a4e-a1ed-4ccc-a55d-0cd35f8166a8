{"version": 3, "file": "semantic-analyzer-service.js", "sourceRoot": "", "sources": ["../../src/services/semantic-analyzer-service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAQjC;;GAEG;AACH,MAAa,uBAAuB;IAEX;IACA;IACA;IAHrB,YACqB,aAA6B,EAC7B,aAA6B,EAC7B,gBAAmC;QAFnC,kBAAa,GAAb,aAAa,CAAgB;QAC7B,kBAAa,GAAb,aAAa,CAAgB;QAC7B,qBAAgB,GAAhB,gBAAgB,CAAmB;IACrD,CAAC;IAEJ;;;;;;;;OAQG;IACI,KAAK,CAAC,mBAAmB,CAC5B,eAAmC,EACnC,cAAsB,EACtB,GAAe,EACf,UAAkB;QAElB,IAAI,CAAC;YACD,wCAAwC;YACxC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,EAAE,CAAC;gBAC3D,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;gBACzD,OAAO,IAAI,CAAC;YAChB,CAAC;YAED,iCAAiC;YACjC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,EAAE,CAAC;gBACxC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,+CAA+C,UAAU,EAAE,CAAC,CAAC;gBACrF,OAAO,IAAI,CAAC;YAChB,CAAC;YAED,kCAAkC;YAClC,IAAI,eAAe,IAAI,IAAI,CAAC,yBAAyB,CAAC,eAAe,EAAE,cAAc,CAAC,EAAE,CAAC;gBACrF,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,wDAAwD,CAAC,CAAC;gBAClF,OAAO,IAAI,CAAC;YAChB,CAAC;YAED,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,kCAAkC,UAAU,UAAU,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC;YAE5F,2CAA2C;YAC3C,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,UAAU,EAAE,GAAG,CAAC,CAAC;YACtF,IAAI,CAAC,cAAc,EAAE,CAAC;gBAClB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;gBAClE,OAAO,IAAI,CAAC;YAChB,CAAC;YAED,wDAAwD;YACxD,IAAI,eAAoD,CAAC;YACzD,IAAI,eAAe,EAAE,CAAC;gBAClB,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,eAAe,EAAE,UAAU,CAAC,CAAC;YACjF,CAAC;YAED,wBAAwB;YACxB,MAAM,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,eAAe,IAAI,EAAE,EAAE,cAAc,CAAC,CAAC;YAE/E,mCAAmC;YACnC,MAAM,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;YACxE,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;YAElD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,gCAAgC,UAAU,CAAC,MAAM,mBAAmB,CAAC,CAAC;YAE9F,OAAO;gBACH,UAAU;gBACV,UAAU;gBACV,UAAU;gBACV,OAAO;aACV,CAAC;QACN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,mCAAmC,KAAK,EAAE,CAAC,CAAC;YACrE,OAAO,IAAI,CAAC;QAChB,CAAC;IACL,CAAC;IAED;;;;OAIG;IACK,mBAAmB,CAAC,UAAkB;QAC1C,MAAM,gBAAgB,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,CAAW,mCAAmC,CAAC,IAAI;YACrG,YAAY,EAAE,YAAY,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK;SACpH,CAAC;QAEF,OAAO,gBAAgB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;IACjD,CAAC;IAED;;;;;OAKG;IACK,yBAAyB,CAAC,eAAuB,EAAE,cAAsB;QAC7E,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,CAAS,4CAA4C,CAAC,IAAI,IAAI,CAAC;QAE9G,+DAA+D;QAC/D,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,MAAM,EAAE,cAAc,CAAC,MAAM,CAAC,CAAC;QAC1E,IAAI,SAAS,KAAK,CAAC,EAAE,CAAC;YAClB,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,MAAM,UAAU,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,MAAM,GAAG,cAAc,CAAC,MAAM,CAAC,GAAG,SAAS,CAAC,CAAC;QAC9F,OAAO,UAAU,GAAG,SAAS,CAAC;IAClC,CAAC;IAED;;;;OAIG;IACK,0BAA0B,CAAC,SAAiB;QAChD,MAAM,sBAAsB,GAA2B;YACnD,KAAK,EAAE,YAAY;YACnB,KAAK,EAAE,YAAY;YACnB,MAAM,EAAE,iBAAiB;YACzB,MAAM,EAAE,iBAAiB;YACzB,KAAK,EAAE,QAAQ;YACf,OAAO,EAAE,MAAM;YACf,KAAK,EAAE,QAAQ;YACf,KAAK,EAAE,IAAI;YACX,KAAK,EAAE,MAAM;YACb,MAAM,EAAE,KAAK;YACb,OAAO,EAAE,MAAM;YACf,MAAM,EAAE,KAAK;YACb,OAAO,EAAE,MAAM;YACf,KAAK,EAAE,UAAU;SACpB,CAAC;QAEF,OAAO,sBAAsB,CAAC,SAAS,CAAC,CAAC;IAC7C,CAAC;IAED;;;;;;OAMG;IACK,KAAK,CAAC,kBAAkB,CAC5B,OAAe,EACf,UAAkB,EAClB,OAAoB;QAEpB,IAAI,CAAC;YACD,yDAAyD;YACzD,IAAI,OAAO,EAAE,CAAC;gBACV,oEAAoE;gBACpE,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;gBACtG,IAAI,OAAO,IAAI,OAAO,CAAC,OAAO,EAAE,KAAK,OAAO,EAAE,CAAC;oBAC3C,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAChD,sCAAsC,EACtC,OAAO,CAAC,GAAG,CACd,CAAC;oBACF,OAAO,OAAO,IAAI,EAAE,CAAC;gBACzB,CAAC;YACL,CAAC;YAED,+CAA+C;YAC/C,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;gBACrD,QAAQ,EAAE,UAAU;gBACpB,OAAO,EAAE,OAAO;aACnB,CAAC,CAAC;YAEH,iDAAiD;YACjD,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAChD,sCAAsC,EACtC,QAAQ,CAAC,GAAG,CACf,CAAC;YAEF,mFAAmF;YACnF,uDAAuD;YAEvD,OAAO,OAAO,IAAI,EAAE,CAAC;QACzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,mCAAmC,KAAK,EAAE,CAAC,CAAC;YACrE,OAAO,SAAS,CAAC;QACrB,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACK,eAAe,CACnB,eAAwC,EACxC,cAAuC;QAEvC,MAAM,UAAU,GAAwB,EAAE,CAAC;QAC3C,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,CAAS,kCAAkC,CAAC,IAAI,CAAC,CAAC;QAEhG,+BAA+B;QAC/B,MAAM,iBAAiB,GAAG,IAAI,GAAG,EAAiC,CAAC;QACnE,MAAM,gBAAgB,GAAG,IAAI,GAAG,EAAiC,CAAC;QAElE,uCAAuC;QACvC,IAAI,CAAC,iBAAiB,CAAC,eAAe,EAAE,iBAAiB,EAAE,EAAE,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC;QAC5E,IAAI,CAAC,iBAAiB,CAAC,cAAc,EAAE,gBAAgB,EAAE,EAAE,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC;QAE1E,qBAAqB;QACrB,KAAK,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,IAAI,gBAAgB,CAAC,OAAO,EAAE,EAAE,CAAC;YACrD,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC9B,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,yBAAyB,CAAC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC;YACxE,CAAC;QACL,CAAC;QAED,uBAAuB;QACvB,KAAK,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,IAAI,iBAAiB,CAAC,OAAO,EAAE,EAAE,CAAC;YACtD,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC7B,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,yBAAyB,CAAC,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;YAC3E,CAAC;QACL,CAAC;QAED,wBAAwB;QACxB,KAAK,MAAM,CAAC,GAAG,EAAE,aAAa,CAAC,IAAI,gBAAgB,CAAC,OAAO,EAAE,EAAE,CAAC;YAC5D,MAAM,cAAc,GAAG,iBAAiB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAClD,IAAI,cAAc,IAAI,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,aAAa,CAAC,EAAE,CAAC;gBACzE,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,cAAc,EAAE,aAAa,EAAE,GAAG,CAAC,CAAC,CAAC;YACtF,CAAC;QACL,CAAC;QAED,sCAAsC;QACtC,IAAI,IAAI,CAAC,aAAa,CAAC,UAAU,CAAU,wCAAwC,CAAC,EAAE,CAAC;YACnF,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;QACnC,CAAC;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;;;;;;OAOG;IACK,iBAAiB,CACrB,OAAgC,EAChC,GAAuC,EACvC,aAAqB,EACrB,YAAoB,EACpB,QAAgB;QAEhB,IAAI,YAAY,IAAI,QAAQ,EAAE,CAAC;YAC3B,OAAO;QACX,CAAC;QAED,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC3B,MAAM,IAAI,GAAG,aAAa,CAAC,CAAC,CAAC,GAAG,aAAa,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC;YAC7E,MAAM,GAAG,GAAG,GAAG,IAAI,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC;YACrC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;YAErB,IAAI,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChD,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,QAAQ,EAAE,GAAG,EAAE,IAAI,EAAE,YAAY,GAAG,CAAC,EAAE,QAAQ,CAAC,CAAC;YACnF,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACK,gBAAgB,CACpB,cAAqC,EACrC,aAAoC;QAEpC,8CAA8C;QAC9C,IAAI,cAAc,CAAC,MAAM,KAAK,aAAa,CAAC,MAAM,EAAE,CAAC;YACjD,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,gCAAgC;QAChC,IAAI,cAAc,CAAC,IAAI,KAAK,aAAa,CAAC,IAAI,EAAE,CAAC;YAC7C,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,+CAA+C;QAC/C,MAAM,aAAa,GAAG,cAAc,CAAC,KAAK,CAAC;QAC3C,MAAM,YAAY,GAAG,aAAa,CAAC,KAAK,CAAC;QACzC,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CACrB,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,GAAG,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC;YACnD,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,GAAG,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CACpD,CAAC;QAEF,IAAI,QAAQ,GAAG,CAAC,EAAE,CAAC;YACf,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;;OAMG;IACK,yBAAyB,CAC7B,MAA6B,EAC7B,SAAiD,EACjD,UAAkB;QAElB,MAAM,UAAU,GAAG,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,EAAE,UAAU,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QAE/G,OAAO;YACH,SAAS;YACT,WAAW,EAAE,IAAI,CAAC,4BAA4B,CAAC,MAAM,CAAC,IAAI,CAAC;YAC3D,WAAW,EAAE,MAAM,CAAC,IAAI;YACxB,SAAS,EAAE,MAAM,CAAC,MAAM;YACxB,KAAK,EAAE;gBACH,SAAS,EAAE,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI;gBAClC,cAAc,EAAE,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS;gBAC5C,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI;gBAC9B,YAAY,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS;aAC3C;YACD,aAAa,EAAE,UAAU,CAAC,CAAC,CAAC;gBACxB,IAAI,EAAE,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE;gBACvC,IAAI,EAAE,SAAS,CAAC,2CAA2C;aAC9D,CAAC,CAAC,CAAC,SAAS;SAChB,CAAC;IACN,CAAC;IAED;;;;;;OAMG;IACK,uBAAuB,CAC3B,cAAqC,EACrC,aAAoC,EACpC,UAAkB;QAElB,MAAM,UAAU,GAAG,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,EAAE,UAAU,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QAE/G,OAAO;YACH,SAAS,EAAE,QAAQ;YACnB,WAAW,EAAE,IAAI,CAAC,4BAA4B,CAAC,aAAa,CAAC,IAAI,CAAC;YAClE,WAAW,EAAE,aAAa,CAAC,IAAI;YAC/B,SAAS,EAAE,aAAa,CAAC,MAAM;YAC/B,KAAK,EAAE;gBACH,SAAS,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI;gBACzC,cAAc,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS;gBACnD,OAAO,EAAE,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI;gBACrC,YAAY,EAAE,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS;aAClD;YACD,aAAa,EAAE,UAAU,CAAC,CAAC,CAAC;gBACxB,IAAI,EAAE,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE;gBACvC,IAAI,EAAE,SAAS;aAClB,CAAC,CAAC,CAAC,SAAS;SAChB,CAAC;IACN,CAAC;IAED;;;;OAIG;IACK,4BAA4B,CAAC,IAAuB;QACxD,QAAQ,IAAI,EAAE,CAAC;YACX,KAAK,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;YAChC,KAAK,MAAM,CAAC,UAAU,CAAC,WAAW;gBAC9B,OAAO,UAAU,CAAC;YACtB,KAAK,MAAM,CAAC,UAAU,CAAC,KAAK;gBACxB,OAAO,OAAO,CAAC;YACnB,KAAK,MAAM,CAAC,UAAU,CAAC,MAAM;gBACzB,OAAO,QAAQ,CAAC;YACpB,KAAK,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;YAChC,KAAK,MAAM,CAAC,UAAU,CAAC,KAAK;gBACxB,OAAO,UAAU,CAAC;YACtB,KAAK,MAAM,CAAC,UAAU,CAAC,SAAS;gBAC5B,OAAO,WAAW,CAAC;YACvB,KAAK,MAAM,CAAC,UAAU,CAAC,IAAI;gBACvB,OAAO,MAAM,CAAC;YAClB,KAAK,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;YAChC,KAAK,MAAM,CAAC,UAAU,CAAC,QAAQ;gBAC3B,OAAO,UAAU,CAAC;YACtB;gBACI,OAAO,SAAS,CAAC;QACzB,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACK,mBAAmB,CAAC,UAA+B,EAAE,cAAuC;QAChG,mEAAmE;QACnE,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9B,OAAO,UAAU,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QAC/C,CAAC;QAED,oDAAoD;QACpD,OAAO,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;IAC7C,CAAC;IAED;;;;OAIG;IACK,gBAAgB,CAAC,UAA+B;QACpD,MAAM,OAAO,GAAG;YACZ,KAAK,EAAE,CAAC;YACR,OAAO,EAAE,CAAC;YACV,QAAQ,EAAE,CAAC;YACX,OAAO,EAAE,CAAC;SACb,CAAC;QAEF,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;YACjC,QAAQ,SAAS,CAAC,SAAS,EAAE,CAAC;gBAC1B,KAAK,KAAK;oBACN,OAAO,CAAC,KAAK,EAAE,CAAC;oBAChB,MAAM;gBACV,KAAK,QAAQ;oBACT,OAAO,CAAC,OAAO,EAAE,CAAC;oBAClB,MAAM;gBACV,KAAK,QAAQ;oBACT,OAAO,CAAC,QAAQ,EAAE,CAAC;oBACnB,MAAM;gBACV,KAAK,QAAQ;oBACT,OAAO,CAAC,OAAO,EAAE,CAAC;oBAClB,MAAM;YACd,CAAC;QACL,CAAC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;;OAIG;IACK,aAAa,CAAC,UAA+B;QACjD,0FAA0F;QAC1F,MAAM,eAAe,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,KAAK,KAAK,CAAC,CAAC;QACtE,MAAM,iBAAiB,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,KAAK,QAAQ,CAAC,CAAC;QAE3E,KAAK,MAAM,KAAK,IAAI,eAAe,EAAE,CAAC;YAClC,KAAK,MAAM,OAAO,IAAI,iBAAiB,EAAE,CAAC;gBACtC,IAAI,KAAK,CAAC,WAAW,KAAK,OAAO,CAAC,WAAW;oBACzC,KAAK,CAAC,SAAS,KAAK,OAAO,CAAC,SAAS;oBACrC,KAAK,CAAC,WAAW,KAAK,OAAO,CAAC,WAAW,EAAE,CAAC;oBAE5C,8BAA8B;oBAC9B,KAAK,CAAC,SAAS,GAAG,QAAQ,CAAC;oBAC3B,KAAK,CAAC,cAAc,GAAG,OAAO,CAAC,WAAW,CAAC;oBAE3C,8DAA8D;oBAC9D,MAAM,YAAY,GAAG,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;oBACjD,IAAI,YAAY,GAAG,CAAC,CAAC,EAAE,CAAC;wBACpB,UAAU,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;oBACvC,CAAC;oBACD,MAAM;gBACV,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;CACJ;AAvdD,0DAudC"}