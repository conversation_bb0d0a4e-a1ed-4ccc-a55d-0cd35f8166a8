{"version": 3, "file": "semantic-analyzer-service.js", "sourceRoot": "", "sources": ["../../src/services/semantic-analyzer-service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,2CAA6B;AAM7B;;GAEG;AACH,MAAa,uBAAuB;IAEX;IACA;IACA;IAHrB,YACqB,aAA6B,EAC7B,aAA6B,EAC7B,gBAAmC;QAFnC,kBAAa,GAAb,aAAa,CAAgB;QAC7B,kBAAa,GAAb,aAAa,CAAgB;QAC7B,qBAAgB,GAAhB,gBAAgB,CAAmB;IACrD,CAAC;IAEJ;;;;;;;OAOG;IACI,KAAK,CAAC,mBAAmB,CAC5B,QAAgB,EAChB,eAAmC,EACnC,cAAkC;QAElC,IAAI,CAAC;YACD,2DAA2D;YAC3D,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YAC7C,IAAI,CAAC,IAAI,CAAC,qCAAqC,CAAC,aAAa,CAAC,EAAE,CAAC;gBAC7D,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,gDAAgD,aAAa,EAAE,CAAC,CAAC;gBACzF,OAAO,IAAI,CAAC;YAChB,CAAC;YAED,mCAAmC;YACnC,MAAM,UAAU,GAAG,IAAI,CAAC,0BAA0B,CAAC,aAAa,CAAC,CAAC;YAClE,IAAI,CAAC,UAAU,EAAE,CAAC;gBACd,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,6CAA6C,QAAQ,EAAE,CAAC,CAAC;gBACjF,OAAO,IAAI,CAAC;YAChB,CAAC;YAED,gFAAgF;YAChF,IAAI,CAAC,eAAe,IAAI,CAAC,cAAc,EAAE,CAAC;gBACtC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,kDAAkD,QAAQ,EAAE,CAAC,CAAC;gBACtF,OAAO,IAAI,CAAC;YAChB,CAAC;YAED,wDAAwD;YACxD,MAAM,eAAe,GAAG,eAAe;gBACnC,CAAC,CAAC,MAAM,IAAI,CAAC,kBAAkB,CAAC,eAAe,EAAE,QAAQ,EAAE,UAAU,CAAC;gBACtE,CAAC,CAAC,EAAE,CAAC;YAET,MAAM,cAAc,GAAG,cAAc;gBACjC,CAAC,CAAC,MAAM,IAAI,CAAC,kBAAkB,CAAC,cAAc,EAAE,QAAQ,EAAE,UAAU,CAAC;gBACrE,CAAC,CAAC,EAAE,CAAC;YAET,iDAAiD;YACjD,MAAM,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,eAAe,EAAE,cAAc,CAAC,CAAC;YAEzE,OAAO;gBACH,UAAU;gBACV,UAAU;aACb,CAAC;QACN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,mCAAmC,KAAK,EAAE,CAAC,CAAC;YACrE,OAAO,IAAI,CAAC;QAChB,CAAC;IACL,CAAC;IAED;;;;OAIG;IACK,qCAAqC,CAAC,SAAiB;QAC3D,MAAM,iBAAiB,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,CAAW,oCAAoC,CAAC,IAAI;YACvG,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM;SAC5E,CAAC;QAEF,OAAO,iBAAiB,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;IACjD,CAAC;IAED;;;;OAIG;IACK,0BAA0B,CAAC,SAAiB;QAChD,MAAM,sBAAsB,GAA2B;YACnD,KAAK,EAAE,YAAY;YACnB,KAAK,EAAE,YAAY;YACnB,MAAM,EAAE,iBAAiB;YACzB,MAAM,EAAE,iBAAiB;YACzB,KAAK,EAAE,QAAQ;YACf,OAAO,EAAE,MAAM;YACf,KAAK,EAAE,QAAQ;YACf,KAAK,EAAE,IAAI;YACX,KAAK,EAAE,MAAM;YACb,MAAM,EAAE,KAAK;YACb,OAAO,EAAE,MAAM;YACf,MAAM,EAAE,KAAK;YACb,OAAO,EAAE,MAAM;YACf,KAAK,EAAE,UAAU;SACpB,CAAC;QAEF,OAAO,sBAAsB,CAAC,SAAS,CAAC,CAAC;IAC7C,CAAC;IAED;;;;;;OAMG;IACK,KAAK,CAAC,kBAAkB,CAC5B,OAAe,EACf,QAAgB,EAChB,UAAkB;QAElB,IAAI,CAAC;YACD,yCAAyC;YACzC,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,EAAE,CAAC;YAC/D,IAAI,CAAC,aAAa,EAAE,CAAC;gBACjB,OAAO,EAAE,CAAC;YACd,CAAC;YAED,8BAA8B;YAC9B,MAAM,OAAO,GAAG,aAAa,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,aAAa,CAAC,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAEjG,+CAA+C;YAC/C,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;gBACrD,QAAQ,EAAE,UAAU;gBACpB,OAAO,EAAE,OAAO;aACnB,CAAC,CAAC;YAEH,iDAAiD;YACjD,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAChD,sCAAsC,EACtC,QAAQ,CAAC,GAAG,CACf,IAAI,EAAE,CAAC;YAER,OAAO,OAAO,CAAC;QACnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,mCAAmC,KAAK,EAAE,CAAC,CAAC;YACrE,OAAO,EAAE,CAAC;QACd,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACK,eAAe,CACnB,eAAwC,EACxC,cAAuC;QAEvC,MAAM,UAAU,GAAwB,EAAE,CAAC;QAE3C,+BAA+B;QAC/B,MAAM,iBAAiB,GAAG,IAAI,GAAG,EAAiC,CAAC;QACnE,MAAM,gBAAgB,GAAG,IAAI,GAAG,EAAiC,CAAC;QAElE,uCAAuC;QACvC,IAAI,CAAC,iBAAiB,CAAC,eAAe,EAAE,iBAAiB,EAAE,EAAE,CAAC,CAAC;QAC/D,IAAI,CAAC,iBAAiB,CAAC,cAAc,EAAE,gBAAgB,EAAE,EAAE,CAAC,CAAC;QAE7D,qBAAqB;QACrB,KAAK,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,IAAI,gBAAgB,CAAC,OAAO,EAAE,EAAE,CAAC;YACrD,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC9B,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,yBAAyB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC;YACrE,CAAC;QACL,CAAC;QAED,uBAAuB;QACvB,KAAK,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,IAAI,iBAAiB,CAAC,OAAO,EAAE,EAAE,CAAC;YACtD,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC7B,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,yBAAyB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC,CAAC;YACvE,CAAC;QACL,CAAC;QAED,wBAAwB;QACxB,KAAK,MAAM,CAAC,GAAG,EAAE,aAAa,CAAC,IAAI,gBAAgB,CAAC,OAAO,EAAE,EAAE,CAAC;YAC5D,MAAM,cAAc,GAAG,iBAAiB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAClD,IAAI,cAAc,IAAI,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,aAAa,CAAC,EAAE,CAAC;gBACzE,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,cAAc,EAAE,aAAa,CAAC,CAAC,CAAC;YACjF,CAAC;QACL,CAAC;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;;;;OAKG;IACK,iBAAiB,CACrB,OAAgC,EAChC,GAAuC,EACvC,aAAqB;QAErB,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC3B,MAAM,IAAI,GAAG,aAAa,CAAC,CAAC,CAAC,GAAG,aAAa,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC;YAC7E,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;YAEtB,IAAI,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChD,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,QAAQ,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;YACvD,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACK,gBAAgB,CACpB,cAAqC,EACrC,aAAoC;QAEpC,8CAA8C;QAC9C,IAAI,cAAc,CAAC,MAAM,KAAK,aAAa,CAAC,MAAM,EAAE,CAAC;YACjD,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,gCAAgC;QAChC,IAAI,cAAc,CAAC,IAAI,KAAK,aAAa,CAAC,IAAI,EAAE,CAAC;YAC7C,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,+CAA+C;QAC/C,MAAM,aAAa,GAAG,cAAc,CAAC,KAAK,CAAC;QAC3C,MAAM,YAAY,GAAG,aAAa,CAAC,KAAK,CAAC;QACzC,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CACrB,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,GAAG,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC;YACnD,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,GAAG,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CACpD,CAAC;QAEF,IAAI,QAAQ,GAAG,CAAC,EAAE,CAAC;YACf,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;OAKG;IACK,yBAAyB,CAC7B,MAA6B,EAC7B,UAAwD;QAExD,OAAO;YACH,IAAI,EAAE,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,IAAI,CAAC;YAC7C,IAAI,EAAE,MAAM,CAAC,IAAI;YACjB,UAAU;YACV,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,KAAK,EAAE;gBACH,SAAS,EAAE,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI;gBAClC,cAAc,EAAE,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS;gBAC5C,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI;gBAC9B,YAAY,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS;aAC3C;YACD,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC;gBACnD,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;gBACjF,CAAC,CAAC,SAAS;SAClB,CAAC;IACN,CAAC;IAED;;;;;OAKG;IACK,uBAAuB,CAC3B,cAAqC,EACrC,aAAoC;QAEpC,OAAO;YACH,IAAI,EAAE,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,IAAI,CAAC;YACpD,IAAI,EAAE,aAAa,CAAC,IAAI;YACxB,UAAU,EAAE,UAAU;YACtB,MAAM,EAAE,aAAa,CAAC,MAAM;YAC5B,cAAc,EAAE,cAAc,CAAC,MAAM;YACrC,KAAK,EAAE;gBACH,SAAS,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI;gBACzC,cAAc,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS;gBACnD,OAAO,EAAE,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI;gBACrC,YAAY,EAAE,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS;aAClD;SACJ,CAAC;IACN,CAAC;IAED;;;;OAIG;IACK,qBAAqB,CAAC,IAAuB;QACjD,QAAQ,IAAI,EAAE,CAAC;YACX,KAAK,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;YAChC,KAAK,MAAM,CAAC,UAAU,CAAC,WAAW;gBAC9B,OAAO,UAAU,CAAC;YACtB,KAAK,MAAM,CAAC,UAAU,CAAC,KAAK;gBACxB,OAAO,OAAO,CAAC;YACnB,KAAK,MAAM,CAAC,UAAU,CAAC,MAAM;gBACzB,OAAO,QAAQ,CAAC;YACpB,KAAK,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;YAChC,KAAK,MAAM,CAAC,UAAU,CAAC,KAAK;gBACxB,OAAO,UAAU,CAAC;YACtB,KAAK,MAAM,CAAC,UAAU,CAAC,SAAS;gBAC5B,OAAO,WAAW,CAAC;YACvB,KAAK,MAAM,CAAC,UAAU,CAAC,IAAI;gBACvB,OAAO,MAAM,CAAC;YAClB,KAAK,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;YAChC,KAAK,MAAM,CAAC,UAAU,CAAC,QAAQ;gBAC3B,OAAO,UAAU,CAAC;YACtB,KAAK,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC;YAC9B,KAAK,MAAM,CAAC,UAAU,CAAC,OAAO;gBAC1B,OAAO,QAAQ,CAAC;YACpB;gBACI,OAAO,OAAO,CAAC;QACvB,CAAC;IACL,CAAC;CACJ;AAvUD,0DAuUC"}