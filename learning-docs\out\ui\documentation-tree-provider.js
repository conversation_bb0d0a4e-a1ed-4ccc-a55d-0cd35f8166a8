"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.DocumentationTreeProvider = exports.FileTreeItem = exports.DocumentationTreeItem = void 0;
const vscode = __importStar(require("vscode"));
const path = __importStar(require("path"));
/**
 * Tree item for a documentation record.
 */
class DocumentationTreeItem extends vscode.TreeItem {
    record;
    collapsibleState;
    constructor(record, collapsibleState) {
        super(DocumentationTreeItem.getLabel(record), collapsibleState);
        this.record = record;
        this.collapsibleState = collapsibleState;
        this.tooltip = `${record.filePath} (${record.timestamp})`;
        this.description = record.timestamp;
        this.id = record.id;
        // Set the icon based on the event type
        switch (record.eventType) {
            case 'create':
                this.iconPath = new vscode.ThemeIcon('add');
                break;
            case 'modify':
                this.iconPath = new vscode.ThemeIcon('edit');
                break;
            case 'delete':
                this.iconPath = new vscode.ThemeIcon('trash');
                break;
        }
        // Set the command to open the documentation panel
        this.command = {
            command: 'learningDocs.showDocumentation',
            title: 'Show Documentation',
            arguments: [record.id]
        };
    }
    /**
     * Get the label for a documentation record.
     * @param record The documentation record.
     * @returns The label for the record.
     */
    static getLabel(record) {
        const fileName = path.basename(record.filePath);
        switch (record.eventType) {
            case 'create':
                return `Created ${fileName}`;
            case 'modify':
                return `Modified ${fileName}`;
            case 'delete':
                return `Deleted ${fileName}`;
            default:
                return fileName;
        }
    }
}
exports.DocumentationTreeItem = DocumentationTreeItem;
/**
 * Tree item for a file.
 */
class FileTreeItem extends vscode.TreeItem {
    filePath;
    collapsibleState;
    constructor(filePath, collapsibleState) {
        super(path.basename(filePath), collapsibleState);
        this.filePath = filePath;
        this.collapsibleState = collapsibleState;
        this.tooltip = filePath;
        this.description = filePath;
        this.contextValue = 'file';
        this.iconPath = vscode.ThemeIcon.File;
    }
}
exports.FileTreeItem = FileTreeItem;
/**
 * Tree data provider for documentation records.
 */
class DocumentationTreeProvider {
    loggerService;
    storageService;
    _onDidChangeTreeData = new vscode.EventEmitter();
    onDidChangeTreeData = this._onDidChangeTreeData.event;
    constructor(loggerService, storageService) {
        this.loggerService = loggerService;
        this.storageService = storageService;
    }
    /**
     * Refresh the tree view.
     */
    refresh() {
        this._onDidChangeTreeData.fire();
    }
    /**
     * Get the tree item for a given element.
     * @param element The element to get the tree item for.
     * @returns The tree item for the element.
     */
    getTreeItem(element) {
        return element;
    }
    /**
     * Get the children of a given element.
     * @param element The element to get the children for.
     * @returns A promise that resolves to the children of the element.
     */
    async getChildren(element) {
        try {
            if (!element) {
                // Root level: show files with documentation
                return await this.getFileItems();
            }
            else if (element instanceof FileTreeItem) {
                // File level: show documentation records for the file
                return await this.getRecordItems(element.filePath);
            }
            else {
                // No children for documentation records
                return [];
            }
        }
        catch (error) {
            this.loggerService.error(`Error getting tree items: ${error}`);
            return [];
        }
    }
    /**
     * Get the file items for the root level.
     * @returns A promise that resolves to the file items.
     */
    async getFileItems() {
        try {
            // Get all unique file paths from the storage
            const filePaths = await this.storageService.getAllFilePaths();
            // Create tree items for each file
            return filePaths.map(filePath => new FileTreeItem(filePath, vscode.TreeItemCollapsibleState.Collapsed));
        }
        catch (error) {
            this.loggerService.error(`Error getting file items: ${error}`);
            return [];
        }
    }
    /**
     * Get the record items for a file.
     * @param filePath The path to the file.
     * @returns A promise that resolves to the record items.
     */
    async getRecordItems(filePath) {
        try {
            // Get records for the file
            const records = await this.storageService.getRecordsByFile(filePath);
            // Create tree items for each record
            return records.map(record => new DocumentationTreeItem(record, vscode.TreeItemCollapsibleState.None));
        }
        catch (error) {
            this.loggerService.error(`Error getting record items: ${error}`);
            return [];
        }
    }
}
exports.DocumentationTreeProvider = DocumentationTreeProvider;
//# sourceMappingURL=documentation-tree-provider.js.map