"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.DocumentationTreeProvider = exports.FileTreeItem = exports.DocumentationTreeItem = exports.SemanticPrimitiveTreeItem = void 0;
const vscode = __importStar(require("vscode"));
const path = __importStar(require("path"));
/**
 * Tree item for a semantic primitive.
 */
class SemanticPrimitiveTreeItem extends vscode.TreeItem {
    primitive;
    recordId;
    constructor(primitive, recordId) {
        super(SemanticPrimitiveTreeItem.getLabel(primitive), vscode.TreeItemCollapsibleState.None);
        this.primitive = primitive;
        this.recordId = recordId;
        this.tooltip = SemanticPrimitiveTreeItem.getTooltip(primitive);
        this.description = primitive.signature || '';
        this.id = `${recordId}-${primitive.elementName}-${primitive.operation}`;
        this.contextValue = 'semanticPrimitive';
        // Set the icon based on the operation type
        switch (primitive.operation) {
            case 'add':
                this.iconPath = new vscode.ThemeIcon('add', new vscode.ThemeColor('charts.green'));
                break;
            case 'remove':
                this.iconPath = new vscode.ThemeIcon('remove', new vscode.ThemeColor('charts.red'));
                break;
            case 'modify':
                this.iconPath = new vscode.ThemeIcon('edit', new vscode.ThemeColor('charts.orange'));
                break;
            case 'rename':
                this.iconPath = new vscode.ThemeIcon('symbol-rename', new vscode.ThemeColor('charts.blue'));
                break;
        }
        // Set the command to show documentation (same as parent record)
        this.command = {
            command: 'learningDocs.showDocumentation',
            title: 'Show Documentation',
            arguments: [recordId]
        };
    }
    /**
     * Get the label for a semantic primitive.
     * @param primitive The semantic primitive.
     * @returns The label for the primitive.
     */
    static getLabel(primitive) {
        const operationSymbol = {
            'add': '➕',
            'remove': '➖',
            'modify': '✏️',
            'rename': '🔄'
        }[primitive.operation];
        const elementTypeCapitalized = primitive.elementType.charAt(0).toUpperCase() + primitive.elementType.slice(1);
        if (primitive.operation === 'rename' && primitive.oldElementName) {
            return `${operationSymbol} ${elementTypeCapitalized}: '${primitive.oldElementName}' → '${primitive.elementName}'`;
        }
        return `${operationSymbol} ${elementTypeCapitalized}: '${primitive.elementName}'`;
    }
    /**
     * Get the tooltip for a semantic primitive.
     * @param primitive The semantic primitive.
     * @returns The tooltip for the primitive.
     */
    static getTooltip(primitive) {
        let tooltip = `${primitive.operation.toUpperCase()} ${primitive.elementType}: ${primitive.elementName}`;
        if (primitive.signature) {
            tooltip += `\nSignature: ${primitive.signature}`;
        }
        if (primitive.parentElement) {
            tooltip += `\nParent: ${primitive.parentElement.type} ${primitive.parentElement.name}`;
        }
        if (primitive.range) {
            tooltip += `\nLocation: Lines ${primitive.range.startLine + 1}-${primitive.range.endLine + 1}`;
        }
        return tooltip;
    }
}
exports.SemanticPrimitiveTreeItem = SemanticPrimitiveTreeItem;
/**
 * Tree item for a documentation record.
 */
class DocumentationTreeItem extends vscode.TreeItem {
    record;
    collapsibleState;
    constructor(record, collapsibleState) {
        super(DocumentationTreeItem.getLabel(record), collapsibleState);
        this.record = record;
        this.collapsibleState = collapsibleState;
        this.tooltip = DocumentationTreeItem.getTooltip(record);
        this.description = record.timestamp;
        this.id = record.id;
        this.contextValue = 'documentationRecord';
        // Set the icon based on the event type
        switch (record.eventType) {
            case 'create':
                this.iconPath = new vscode.ThemeIcon('add');
                break;
            case 'modify':
                this.iconPath = new vscode.ThemeIcon('edit');
                break;
            case 'delete':
                this.iconPath = new vscode.ThemeIcon('trash');
                break;
            case 'baseline':
                this.iconPath = new vscode.ThemeIcon('file');
                break;
        }
        // Set the command to open the documentation panel
        this.command = {
            command: 'learningDocs.showDocumentation',
            title: 'Show Documentation',
            arguments: [record.id]
        };
    }
    /**
     * Get the label for a documentation record.
     * @param record The documentation record.
     * @returns The label for the record.
     */
    static getLabel(record) {
        const fileName = path.basename(record.filePath);
        let baseLabel;
        switch (record.eventType) {
            case 'create':
                baseLabel = `Created ${fileName}`;
                break;
            case 'modify':
                baseLabel = `Modified ${fileName}`;
                break;
            case 'delete':
                baseLabel = `Deleted ${fileName}`;
                break;
            case 'baseline':
                baseLabel = `Baseline ${fileName}`;
                break;
            default:
                baseLabel = fileName;
        }
        // Add semantic primitives count if available
        if (record.semanticPrimitives && record.semanticPrimitives.length > 0) {
            const count = record.semanticPrimitives.length;
            baseLabel += ` (${count} semantic change${count > 1 ? 's' : ''})`;
        }
        return baseLabel;
    }
    /**
     * Get the tooltip for a documentation record.
     * @param record The documentation record.
     * @returns The tooltip for the record.
     */
    static getTooltip(record) {
        let tooltip = `${record.filePath} (${record.timestamp})`;
        if (record.semanticPrimitives && record.semanticPrimitives.length > 0) {
            tooltip += '\n\nSemantic Changes:';
            record.semanticPrimitives.forEach(primitive => {
                const operationSymbol = {
                    'add': '➕',
                    'remove': '➖',
                    'modify': '✏️',
                    'rename': '🔄'
                }[primitive.operation];
                tooltip += `\n${operationSymbol} ${primitive.elementType}: ${primitive.elementName}`;
            });
        }
        return tooltip;
    }
}
exports.DocumentationTreeItem = DocumentationTreeItem;
/**
 * Tree item for a file.
 */
class FileTreeItem extends vscode.TreeItem {
    filePath;
    collapsibleState;
    constructor(filePath, collapsibleState) {
        super(path.basename(filePath), collapsibleState);
        this.filePath = filePath;
        this.collapsibleState = collapsibleState;
        this.tooltip = filePath;
        this.description = filePath;
        this.contextValue = 'file';
        this.iconPath = vscode.ThemeIcon.File;
    }
}
exports.FileTreeItem = FileTreeItem;
/**
 * Tree data provider for documentation records.
 */
class DocumentationTreeProvider {
    loggerService;
    storageService;
    _onDidChangeTreeData = new vscode.EventEmitter();
    onDidChangeTreeData = this._onDidChangeTreeData.event;
    constructor(loggerService, storageService) {
        this.loggerService = loggerService;
        this.storageService = storageService;
    }
    /**
     * Refresh the tree view.
     */
    refresh() {
        this._onDidChangeTreeData.fire();
    }
    /**
     * Get the tree item for a given element.
     * @param element The element to get the tree item for.
     * @returns The tree item for the element.
     */
    getTreeItem(element) {
        return element;
    }
    /**
     * Get the children of a given element.
     * @param element The element to get the children for.
     * @returns A promise that resolves to the children of the element.
     */
    async getChildren(element) {
        try {
            if (!element) {
                // Root level: show files with documentation
                return await this.getFileItems();
            }
            else if (element instanceof FileTreeItem) {
                // File level: show documentation records for the file
                return await this.getRecordItems(element.filePath);
            }
            else if (element instanceof DocumentationTreeItem) {
                // Documentation record level: show semantic primitives if available
                return this.getSemanticPrimitiveItems(element.record);
            }
            else {
                // No children for semantic primitives
                return [];
            }
        }
        catch (error) {
            this.loggerService.error(`Error getting tree items: ${error}`);
            return [];
        }
    }
    /**
     * Get the file items for the root level.
     * @returns A promise that resolves to the file items.
     */
    async getFileItems() {
        try {
            // Get all unique file paths from the storage
            const filePaths = await this.storageService.getAllFilePaths();
            // Create tree items for each file
            return filePaths.map(filePath => new FileTreeItem(filePath, vscode.TreeItemCollapsibleState.Collapsed));
        }
        catch (error) {
            this.loggerService.error(`Error getting file items: ${error}`);
            return [];
        }
    }
    /**
     * Get the record items for a file.
     * @param filePath The path to the file.
     * @returns A promise that resolves to the record items.
     */
    async getRecordItems(filePath) {
        try {
            // Get records for the file
            const records = await this.storageService.getRecordsByFile(filePath);
            // Create tree items for each record
            return records.map(record => {
                // Set collapsible state based on whether record has semantic primitives
                const hasSemanticPrimitives = record.semanticPrimitives && record.semanticPrimitives.length > 0;
                const collapsibleState = hasSemanticPrimitives
                    ? vscode.TreeItemCollapsibleState.Collapsed
                    : vscode.TreeItemCollapsibleState.None;
                return new DocumentationTreeItem(record, collapsibleState);
            });
        }
        catch (error) {
            this.loggerService.error(`Error getting record items: ${error}`);
            return [];
        }
    }
    /**
     * Get the semantic primitive items for a documentation record.
     * @param record The documentation record.
     * @returns An array of semantic primitive tree items.
     */
    getSemanticPrimitiveItems(record) {
        if (!record.semanticPrimitives || record.semanticPrimitives.length === 0) {
            return [];
        }
        return record.semanticPrimitives.map(primitive => new SemanticPrimitiveTreeItem(primitive, record.id));
    }
}
exports.DocumentationTreeProvider = DocumentationTreeProvider;
//# sourceMappingURL=documentation-tree-provider.js.map