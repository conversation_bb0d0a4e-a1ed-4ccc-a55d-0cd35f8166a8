{"version": 3, "file": "documentation-panel.js", "sourceRoot": "", "sources": ["../../src/ui/documentation-panel.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAMjC;;GAEG;AACH,MAAa,kBAAkB;IAUN;IACA;IAVd,MAAM,CAAU,QAAQ,GAAG,iCAAiC,CAAC;IAEnD,MAAM,CAAsB;IAC5B,aAAa,CAAa;IACnC,YAAY,GAAwB,EAAE,CAAC;IAE/C,YACI,KAA0B,EAC1B,YAAwB,EACP,aAA6B,EAC7B,cAA+B;QAD/B,kBAAa,GAAb,aAAa,CAAgB;QAC7B,mBAAc,GAAd,cAAc,CAAiB;QAEhD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAElC,yCAAyC;QACzC,IAAI,CAAC,OAAO,EAAE,CAAC;QAEf,wCAAwC;QACxC,2FAA2F;QAC3F,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QAExE,2CAA2C;QAC3C,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAC5B,CAAC,CAAC,EAAE;YACA,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gBACtB,IAAI,CAAC,OAAO,EAAE,CAAC;YACnB,CAAC;QACL,CAAC,EACD,IAAI,EACJ,IAAI,CAAC,YAAY,CACpB,CAAC;QAEF,mCAAmC;QACnC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,mBAAmB,CACnC,OAAO,CAAC,EAAE;YACN,QAAQ,OAAO,CAAC,OAAO,EAAE,CAAC;gBACtB,KAAK,YAAY;oBACb,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;oBACnC,OAAO;YACf,CAAC;QACL,CAAC,EACD,IAAI,EACJ,IAAI,CAAC,YAAY,CACpB,CAAC;IACN,CAAC;IAED;;;;;;OAMG;IACI,MAAM,CAAC,YAAY,CACtB,YAAwB,EACxB,aAA6B,EAC7B,cAA+B;QAE/B,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB;YACzC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,UAAU;YAC3C,CAAC,CAAC,SAAS,CAAC;QAEhB,sCAAsC;QACtC,IAAI,kBAAkB,CAAC,YAAY,EAAE,CAAC;YAClC,kBAAkB,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YACtD,OAAO,kBAAkB,CAAC,YAAY,CAAC;QAC3C,CAAC;QAED,gCAAgC;QAChC,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAC1C,kBAAkB,CAAC,QAAQ,EAC3B,wBAAwB,EACxB,MAAM,IAAI,MAAM,CAAC,UAAU,CAAC,GAAG,EAC/B;YACI,mCAAmC;YACnC,aAAa,EAAE,IAAI;YAEnB,iEAAiE;YACjE,kBAAkB,EAAE;gBAChB,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,YAAY,EAAE,OAAO,CAAC;aAC7C;YAED,6BAA6B;YAC7B,uBAAuB,EAAE,IAAI;SAChC,CACJ,CAAC;QAEF,kBAAkB,CAAC,YAAY,GAAG,IAAI,kBAAkB,CAAC,KAAK,EAAE,YAAY,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;QAC7G,OAAO,kBAAkB,CAAC,YAAY,CAAC;IAC3C,CAAC;IAED;;;OAGG;IACI,KAAK,CAAC,wBAAwB,CAAC,QAAgB;QAClD,IAAI,CAAC;YACD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,mCAAmC,QAAQ,EAAE,CAAC,CAAC;YAEvE,2BAA2B;YAC3B,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YAErE,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACvB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;gBAClE,OAAO;YACX,CAAC;YAED,8BAA8B;YAC9B,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAC1C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,yCAAyC,KAAK,EAAE,CAAC,CAAC;YAC3E,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,oCAAoC,QAAQ,EAAE,CAAC,CAAC;QAClG,CAAC;IACL,CAAC;IAED;;;OAGG;IACI,KAAK,CAAC,0BAA0B,CAAC,QAAgB;QACpD,IAAI,CAAC;YACD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,qCAAqC,QAAQ,EAAE,CAAC,CAAC;YACzE,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QACrC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,2CAA2C,KAAK,EAAE,CAAC,CAAC;YAC7E,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,yBAAyB,QAAQ,EAAE,CAAC,CAAC;QACvF,CAAC;IACL,CAAC;IAED;;;OAGG;IACK,KAAK,CAAC,WAAW,CAAC,QAAgB;QACtC,IAAI,CAAC;YACD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YAEjE,IAAI,CAAC,MAAM,EAAE,CAAC;gBACV,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,qBAAqB,QAAQ,EAAE,CAAC,CAAC;gBAC/E,OAAO;YACX,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;QAClE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,yBAAyB,KAAK,EAAE,CAAC,CAAC;YAC3D,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,yBAAyB,QAAQ,EAAE,CAAC,CAAC;QACvF,CAAC;IACL,CAAC;IAED;;OAEG;IACK,OAAO;QACX,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,wBAAwB,CAAC;QAC7C,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;IACtD,CAAC;IAED;;;OAGG;IACK,eAAe;QACnB,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBA4BC,CAAC;IACb,CAAC;IAED;;;;OAIG;IACK,qBAAqB,CAAC,MAAiC;QAC3D,OAAO;;;;;uCAKwB,MAAM,CAAC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wCAqDd,MAAM,CAAC,QAAQ;mDACJ,MAAM,CAAC,SAAS;iDAClB,MAAM,CAAC,SAAS;;kBAE/C,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;;kBAExD,MAAM,CAAC,kBAAkB,IAAI,MAAM,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACjE,IAAI,CAAC,0BAA0B,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;;kBAE9C,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;;kBAE/C,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;;;gBAGlE,CAAC;IACb,CAAC;IAED;;;;OAIG;IACK,kBAAkB,CAAC,MAAiC;QACxD,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;YACrB,OAAO,EAAE,CAAC;QACd,CAAC;QAED,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,MAAM,EAAE,WAAW,EAAE,iBAAiB,EAAE,YAAY,EAAE,GAAG,MAAM,CAAC,UAAU,CAAC;QAEzG,OAAO;;;;iBAIE,OAAO;;;iBAGP,WAAW;;cAEd,MAAM,CAAC,CAAC,CAAC,qBAAqB,MAAM,MAAM,CAAC,CAAC,CAAC,EAAE;;cAE/C,WAAW,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;;;sBAGhC,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC,OAAO,UAAU,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;;aAEzE,CAAC,CAAC,CAAC,EAAE;;cAEJ,iBAAiB,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;;;sBAG5C,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;;sCAElB,QAAQ,CAAC,KAAK;8BACtB,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,YAAY,QAAQ,CAAC,GAAG,KAAK,QAAQ,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE;iCAChE,QAAQ,CAAC,WAAW;;qBAEhC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;;aAElB,CAAC,CAAC,CAAC,EAAE;;cAEJ,YAAY,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;;kBAEtC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;;8BAEhB,OAAO,CAAC,KAAK;qCACN,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC;6BACtC,OAAO,CAAC,WAAW;;iBAE/B,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;aACd,CAAC,CAAC,CAAC,EAAE;eACH,CAAC;IACZ,CAAC;IAED;;;;OAIG;IACK,0BAA0B,CAAC,MAAiC;QAChE,IAAI,CAAC,MAAM,CAAC,kBAAkB,IAAI,MAAM,CAAC,kBAAkB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvE,OAAO,EAAE,CAAC;QACd,CAAC;QAED,OAAO;;;cAGD,MAAM,CAAC,kBAAkB,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;kDACP,SAAS,CAAC,UAAU;8BACxC,SAAS,CAAC,IAAI,IAAI,SAAS,CAAC,IAAI,cAAc,SAAS,CAAC,UAAU;sBAC1E,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,cAAc,SAAS,CAAC,MAAM,MAAM,CAAC,CAAC,CAAC,EAAE;sBAC5D,SAAS,CAAC,cAAc,CAAC,CAAC,CAAC,uBAAuB,SAAS,CAAC,cAAc,MAAM,CAAC,CAAC,CAAC,EAAE;sBACrF,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC,iBAAiB,SAAS,CAAC,aAAa,MAAM,CAAC,CAAC,CAAC,EAAE;;aAEtF,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;eACR,CAAC;IACZ,CAAC;IAED;;;;OAIG;IACK,YAAY,CAAC,MAAiC;QAClD,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YAClB,OAAO,EAAE,CAAC;QACd,CAAC;QAED,8CAA8C;QAC9C,MAAM,aAAa,GAAG,MAAM,CAAC,OAAO;aAC/B,KAAK,CAAC,IAAI,CAAC;aACX,GAAG,CAAC,IAAI,CAAC,EAAE;YACR,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;gBACvB,OAAO,2BAA2B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC;YACrE,CAAC;iBAAM,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC9B,OAAO,6BAA6B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC;YACvE,CAAC;iBAAM,CAAC;gBACJ,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YAClC,CAAC;QACL,CAAC,CAAC;aACD,IAAI,CAAC,IAAI,CAAC,CAAC;QAEhB,OAAO;;;yBAGU,aAAa;eACvB,CAAC;IACZ,CAAC;IAED;;;;OAIG;IACK,sBAAsB,CAAC,MAAiC;QAC5D,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;YACzB,OAAO,EAAE,CAAC;QACd,CAAC;QAED,MAAM,EAAE,YAAY,EAAE,UAAU,EAAE,cAAc,EAAE,mBAAmB,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,MAAM,CAAC,cAAc,CAAC;QAE7H,OAAO;;;;cAID,YAAY,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;;;sBAGlC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;;aAE9D,CAAC,CAAC,CAAC,EAAE;;cAEJ,UAAU,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;;;sBAG9B,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;;sCAEN,GAAG,CAAC,QAAQ,IAAI,GAAG,CAAC,UAAU;yCAC3B,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC;;qBAEjD,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;;aAElB,CAAC,CAAC,CAAC,EAAE;;cAEJ,cAAc,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;;;sBAGtC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,OAAO,GAAG,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;;aAE9D,CAAC,CAAC,CAAC,EAAE;;cAEJ,mBAAmB,IAAI,mBAAmB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;;;sBAGhD,mBAAmB,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,OAAO,GAAG,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;;aAEnE,CAAC,CAAC,CAAC,EAAE;;cAEJ,aAAa,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;;;sBAGpC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;;sCAEZ,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;iCACtC,MAAM,CAAC,OAAO,KAAK,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC,IAAI;;qBAE5D,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;;aAElB,CAAC,CAAC,CAAC,EAAE;;cAEJ,YAAY,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;;;sBAGlC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;;aAE9D,CAAC,CAAC,CAAC,EAAE;eACH,CAAC;IACZ,CAAC;IAED;;;;OAIG;IACK,uBAAuB,CAAC,QAAgB;QAC5C,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;wDAyByC,QAAQ;;;;gBAIhD,CAAC;IACb,CAAC;IAED;;;;OAIG;IACK,aAAa,CAAC,YAAoB;QACtC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCA4BoB,YAAY;;;gBAG/B,CAAC;IACb,CAAC;IAED;;;;OAIG;IACK,WAAW,CAAC,IAAY;QAC5B,OAAO,IAAI;aACN,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC;aACtB,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;aACrB,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;aACrB,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC;aACvB,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,OAAO;QACV,kBAAkB,CAAC,YAAY,GAAG,SAAS,CAAC;QAE5C,yBAAyB;QACzB,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;QAEtB,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;YAC9B,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC;YAC3C,IAAI,UAAU,EAAE,CAAC;gBACb,UAAU,CAAC,OAAO,EAAE,CAAC;YACzB,CAAC;QACL,CAAC;IACL,CAAC;IAEO,MAAM,CAAC,YAAY,CAAiC;;AAtjBhE,gDAujBC"}