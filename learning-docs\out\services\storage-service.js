"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.StorageService = void 0;
const path = __importStar(require("path"));
const fs = __importStar(require("fs"));
/**
 * Implementation of the storage service.
 */
class StorageService {
    context;
    loggerService;
    workspaceService;
    storageDir;
    constructor(context, loggerService, workspaceService) {
        this.context = context;
        this.loggerService = loggerService;
        this.workspaceService = workspaceService;
        // Create storage directory within the extension's global storage
        this.storageDir = path.join(context.globalStorageUri.fsPath, 'documentation');
        this.ensureStorageDirectoryExists();
    }
    /**
     * Save a documentation record.
     * @param record The record to save.
     */
    async saveDocumentationRecord(record) {
        try {
            const workspaceId = this.getWorkspaceId();
            const workspaceDir = path.join(this.storageDir, workspaceId);
            // Ensure workspace directory exists
            if (!fs.existsSync(workspaceDir)) {
                fs.mkdirSync(workspaceDir, { recursive: true });
            }
            // Create a file path for the record
            const recordPath = path.join(workspaceDir, `${record.id}.json`);
            // Write the record to the file
            await fs.promises.writeFile(recordPath, JSON.stringify(record, null, 2));
            this.loggerService.info(`Saved documentation record: ${record.id}`);
        }
        catch (error) {
            this.loggerService.error(`Failed to save documentation record: ${error}`);
            throw error;
        }
    }
    /**
     * Get a record by its ID.
     * @param id The ID of the record to retrieve.
     * @returns The record, or undefined if not found.
     */
    async getRecordById(id) {
        try {
            const workspaceId = this.getWorkspaceId();
            const recordPath = path.join(this.storageDir, workspaceId, `${id}.json`);
            if (!fs.existsSync(recordPath)) {
                return undefined;
            }
            const content = await fs.promises.readFile(recordPath, 'utf-8');
            return JSON.parse(content);
        }
        catch (error) {
            this.loggerService.error(`Failed to get record by ID: ${error}`);
            return undefined;
        }
    }
    /**
     * Get all records for a specific file.
     * @param filePath The path to the file.
     * @returns An array of records for the file.
     */
    async getRecordsByFile(filePath) {
        try {
            const workspaceId = this.getWorkspaceId();
            const workspaceDir = path.join(this.storageDir, workspaceId);
            if (!fs.existsSync(workspaceDir)) {
                return [];
            }
            const files = await fs.promises.readdir(workspaceDir);
            const records = [];
            for (const file of files) {
                if (path.extname(file) === '.json') {
                    const recordPath = path.join(workspaceDir, file);
                    const content = await fs.promises.readFile(recordPath, 'utf-8');
                    const record = JSON.parse(content);
                    if (record.filePath === filePath) {
                        records.push(record);
                    }
                }
            }
            // Sort by timestamp (newest first)
            return records.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
        }
        catch (error) {
            this.loggerService.error(`Failed to get records by file: ${error}`);
            return [];
        }
    }
    /**
     * Get all file paths that have documentation records.
     * @returns An array of file paths.
     */
    async getAllFilePaths() {
        try {
            const workspaceId = this.getWorkspaceId();
            const workspaceDir = path.join(this.storageDir, workspaceId);
            if (!fs.existsSync(workspaceDir)) {
                return [];
            }
            const files = await fs.promises.readdir(workspaceDir);
            const filePaths = new Set();
            for (const file of files) {
                if (path.extname(file) === '.json') {
                    const recordPath = path.join(workspaceDir, file);
                    const content = await fs.promises.readFile(recordPath, 'utf-8');
                    const record = JSON.parse(content);
                    if (record.filePath) {
                        filePaths.add(record.filePath);
                    }
                }
            }
            return Array.from(filePaths).sort();
        }
        catch (error) {
            this.loggerService.error(`Failed to get all file paths: ${error}`);
            return [];
        }
    }
    /**
     * Ensure the storage directory exists.
     */
    ensureStorageDirectoryExists() {
        if (!fs.existsSync(this.storageDir)) {
            fs.mkdirSync(this.storageDir, { recursive: true });
        }
    }
    /**
     * Get a unique identifier for the current workspace.
     * @returns A string identifier for the workspace.
     */
    getWorkspaceId() {
        const workspaceRoot = this.workspaceService.getWorkspaceRoot();
        if (!workspaceRoot) {
            return 'no-workspace';
        }
        // Use the last segment of the workspace path as an identifier
        return path.basename(workspaceRoot.fsPath);
    }
}
exports.StorageService = StorageService;
//# sourceMappingURL=storage-service.js.map